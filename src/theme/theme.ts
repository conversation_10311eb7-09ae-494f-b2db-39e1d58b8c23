import { Dimensions, StyleSheet, Platform } from 'react-native';

export const colors = {
  white: '#fff',
  black: '#000',
  primary: '#0873B4',
  light_Secondary: '#3C3C4399',
  cyan_blue: '#CDE3FF',
  background_color: '#fafafa',
  warning: '#E35151',
  eastBay: '#475569',
  green: '#4E4B66',
  grey_05: '#D1D1D6',
  grey_10: '#ECEFF2',
  grey_20: '#D9DFE6',
  grey_30: '#606873',
  grey_50: '#A0AEC0',
  grey_60: '#808B9A',
  //  grey_70: '#606873',
  grey_80: '#39434F',
  grey_90: '#202326',
  grey_95: '#333333',
  grey_100: '#101113',
};

export const fonts = {
  Catamaran_Light: 'Catamaran-Light',
  Catamaran_Regular: 'Catamaran-Regular',
  Catamaran_Medium: 'Catamaran-Medium',
  Catamaran_SemiBold: 'Catamaran-SemiBold',
  Catamaran_Bold: 'Catamaran-Bold',
  NotoSans_Light: 'NotoSans-Light',
  NotoSans_Regular: 'NotoSans-Regular',
  NotoSans_Medium: 'NotoSans-Medium',
  NotoSans_MediumItalic: 'NotoSans-MediumItalic',
  NotoSans_SemiBold: 'NotoSans-SemiBold',
  NotoSans_Bold: 'NotoSans-Bold',
  Roboto_Light: 'Roboto-Light',
  Roboto_Regular: 'Roboto-Regular',
  Roboto_Medium: 'Roboto-Medium',
  Roboto_SemiBold: 'Roboto-SemiBold',
  Roboto_Bold: 'Roboto-Bold',
};

export const sizes = {
  width: Dimensions.get('screen').width,
  height: Dimensions.get('screen').height,
  paddingHorizontal: 24,
};

export const appStyles = StyleSheet.create({
  flexRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  flexBtw: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitleStyle: {
    fontSize: 18,
    fontFamily: fonts.Catamaran_Bold,
    color: colors.grey_80,
    lineHeight: 28,
    paddingLeft: 10,
    paddingTop: 3,
  },
  footerText: {
    fontSize: 14,
    fontFamily: fonts.NotoSans_Medium,
    color: colors.grey_30,
    textAlign: 'center',
    marginTop: 24,
  },
  signupText: {
    fontSize: 13,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.primary,
  },
  orContainer: {
    flexDirection: 'row',
    marginTop: 32,
    alignItems: 'center',
  },
  horizontalLine: {
    height: 1,
    backgroundColor: colors.grey_20,
    flex: 1,
  },
  errorText: {
    color: colors.warning,
    fontSize: 12,
    fontFamily: fonts.NotoSans_Medium,
    marginTop: 5,
    paddingLeft: 16,
  },
  largeTitle: {
    fontSize: 26,
    fontFamily: fonts.Catamaran_Bold,
    color: colors.grey_30,
    textAlign: 'center',
    lineHeight: 34,
  },
  h1: {
    fontSize: 20,
    fontFamily: fonts.NotoSans_SemiBold,
    color: colors.black,
    lineHeight: 20,
  },
  h2: {
    fontSize: 18,
    fontFamily: fonts.NotoSans_Medium,
    color: colors.green,
    textAlign: 'center',
  },
  h3: {
    fontSize: 16,
    fontFamily: fonts.Catamaran_SemiBold,
    color: colors.grey_90,
  },
  h4: {
    fontSize: 14,
    fontFamily: fonts.Catamaran_Medium,
    color: '#858583',
  },
  h5: {
    fontSize: 12,
    fontFamily: fonts.Catamaran_Medium,
    color: '#F97066',
  },
  body1: {
    fontSize: 16,
    color: '#636363',
    fontFamily: fonts.NotoSans_Regular,
    textAlign: 'center',
  },
  body2: {
    fontSize: 14,
    color: colors.grey_60,
    fontFamily: fonts.NotoSans_Regular,
  },
  body3: {
    fontSize: 12,
    color: colors.black,
    fontFamily: fonts.NotoSans_Medium,
  },
});
