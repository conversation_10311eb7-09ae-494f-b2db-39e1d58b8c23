import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { AppText, Avatar, FormInput, AppButton } from '../../../componets';
import { colors, fonts, sizes } from '../../../theme/theme';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { EditIcon } from '../../../assets/svgs';

// ✅ Validation schema
const EditProfileSchema = Yup.object().shape({
  fullName: Yup.string().required('Full Name is required'),
  lastName: Yup.string().required('Last Name is required'),
  dob: Yup.string().required('DOB is required'),
  gender: Yup.string().required('Gender is required'),
  height: Yup.string().required('Height is required'),
  weight: Yup.string().required('Weight is required'),
  modeSelection: Yup.string().required('Mode is required'),
});

const EditProfile = () => {
  const formik = useFormik({
    initialValues: {
      fullName: 'John',
      lastName: 'Doe',
      dob: '1995-01-01',
      gender: 'Male',
      height: '180',
      weight: '75',
      modeSelection: 'Manual',
    },
    validationSchema: EditProfileSchema,
    enableReinitialize: true,
    onSubmit: values => {
      console.log('Updated Profile:', values);
    },
  });

  const {
    handleChange,
    handleBlur,
    handleSubmit,
    values,
    errors,
    touched,
    isValid,
    dirty,
  } = formik;

  return (
    <KeyboardAwareScrollView
      contentContainerStyle={styles.scrollContent}
      showsVerticalScrollIndicator={false}
      keyboardShouldPersistTaps="handled"
    >
      <View style={styles.container}>
        {/* Profile Avatar */}
        <Avatar
          size={100}
          uri="https://plus.unsplash.com/premium_photo-1689977807477-a579eda91fa2?w=900"
          containerStyle={styles.avatar}
        />

        {/* Change Picture Link */}
        <TouchableOpacity activeOpacity={0.5}>
          <AppText style={styles.changePictureText}>Change Picture</AppText>
        </TouchableOpacity>

        {/* Section Heading */}
        <AppText style={styles.sectionTitle}>Personal Information</AppText>

        <View style={styles.formContainer}>
          <FormInput
            label="Full Name"
            value={values.fullName}
            onChangeText={handleChange('fullName')}
            onBlur={handleBlur('fullName')}
            error={touched.fullName && errors.fullName ? errors.fullName : ''}
            rightIcon={<EditIcon />}
          />
          <FormInput
            label="Last Name"
            value={values.lastName}
            onChangeText={handleChange('lastName')}
            onBlur={handleBlur('lastName')}
            error={touched.lastName && errors.lastName ? errors.lastName : ''}
            rightIcon={<EditIcon />}
          />
          <FormInput
            label="DOB"
            value={values.dob}
            onChangeText={handleChange('dob')}
            onBlur={handleBlur('dob')}
            error={touched.dob && errors.dob ? errors.dob : ''}
            rightIcon={<EditIcon />}
          />
          <FormInput
            label="Gender"
            value={values.gender}
            onChangeText={handleChange('gender')}
            onBlur={handleBlur('gender')}
            error={touched.gender && errors.gender ? errors.gender : ''}
            rightIcon={<EditIcon />}
          />
          <FormInput
            label="Height"
            value={values.height}
            onChangeText={handleChange('height')}
            onBlur={handleBlur('height')}
            error={touched.height && errors.height ? errors.height : ''}
            rightIcon={<EditIcon />}
          />
          <FormInput
            label="Weight"
            value={values.weight}
            onChangeText={handleChange('weight')}
            onBlur={handleBlur('weight')}
            error={touched.weight && errors.weight ? errors.weight : ''}
            rightIcon={<EditIcon />}
          />
          <FormInput
            label="Mode selection"
            value={values.modeSelection}
            onChangeText={handleChange('modeSelection')}
            onBlur={handleBlur('modeSelection')}
            error={
              touched.modeSelection && errors.modeSelection
                ? errors.modeSelection
                : ''
            }
            rightIcon={<EditIcon />}
          />
        </View>

        {/* Submit Button */}
        <AppButton
          title="Save Changes"
          onPress={handleSubmit}
          containerStyle={styles.submitButton}
          disabled={!isValid || !dirty}
        />
      </View>
    </KeyboardAwareScrollView>
  );
};

export default EditProfile;

const styles = StyleSheet.create({
  scrollContent: {
    flexGrow: 1,
  },
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 42,
    paddingBottom: 40,
  },
  avatar: {
    alignSelf: 'center',
  },
  changePictureText: {
    paddingTop: 6,
    textAlign: 'center',
    textDecorationLine: 'underline',
    fontFamily: fonts.NotoSans_SemiBold,
    fontSize: 14,
    color: colors.primary,
  },
  sectionTitle: {
    paddingTop: 32,
    fontFamily: fonts.NotoSans_SemiBold,
    fontSize: 14,
    color: colors.grey_80,
  },
  formContainer: {
    marginTop: 14,
    gap: 14,
  },
  submitButton: {
    marginTop: 28,
  },
});
