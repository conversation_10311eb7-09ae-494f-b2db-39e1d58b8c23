import React from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  StyleProp,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { appStyles, colors, fonts } from '../../theme/theme';
import Avatar from '../common/Avatar';
import AppText from '../common/AppText';
import { ChevronRight } from '../../assets/svgs';

interface Props {
  onPress?: () => void;
  name?: string;
  subtitle?: string;
  avatarUri?: string;
  containerStyle?: StyleProp<ViewStyle>;
  size?: number;
  nameStyle?: TextStyle;
  subTitleStyle?: TextStyle;
  isChevron?: boolean;
  chevronColor?: string;
  icon?: React.ReactNode;
  onPressIcon?: () => void;
}

const ProfileHeader: React.FC<Props> = ({
  onPress,
  name = '<PERSON> Rojas',
  subtitle = 'Show & edit profile',
  avatarUri = 'https://plus.unsplash.com/premium_photo-1689568126014-06fea9d5d341?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8cHJvZmlsZXxlbnwwfHwwfHx8MA%3D%3D',
  containerStyle,
  size,
  nameStyle,
  subTitleStyle,
  isChevron = true,
  chevronColor = colors.grey_100,
  icon,
  onPressIcon,
}) => {
  return (
    <TouchableOpacity
      disabled={!onPress}
      activeOpacity={0.5}
      style={[appStyles.flexBtw, styles.container, containerStyle]}
      onPress={onPress}
    >
      <View style={styles.profileInfo}>
        <Avatar uri={avatarUri} size={size ? size : 50} />
        <View>
          <AppText style={[styles.name, nameStyle]}>{name}</AppText>
          <AppText style={[styles.subtitle, subTitleStyle]}>{subtitle}</AppText>
        </View>
      </View>
      {isChevron ? (
        <ChevronRight stroke={chevronColor} />
      ) : (
        <TouchableOpacity
          hitSlop={20}
          activeOpacity={0.4}
          onPress={onPressIcon}
        >
          {icon}
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );
};

export default ProfileHeader;

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  profileInfo: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
  name: {
    ...appStyles.h4,
    color: colors.grey_80,
  },
  subtitle: {
    ...appStyles.body3,
    fontFamily: fonts.Catamaran_Regular,
    lineHeight: 19,
  },
});
