import React from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
} from 'react-native';
import { appStyles, colors, fonts } from '../../theme/theme';
import AppText from '../common/AppText';
import HorizontalLine from '../common/HorizontalLine';

export interface HealthCardProps {
  title: string;
  value: string | number;
  unit?: string;
  icon: React.ReactNode;
  subtitle: string;
  containerStyle?: ViewStyle;
  titleStyle?: TextStyle;
  valueStyle?: TextStyle;
  subtitleStyle?: TextStyle;
  onPressCard?: (val: string) => void;
}

const HealthCard: React.FC<HealthCardProps> = ({
  title,
  value,
  unit,
  icon,
  subtitle,
  containerStyle,
  titleStyle,
  valueStyle,
  subtitleStyle,
  onPressCard,
}) => {
  return (
    <TouchableOpacity
      activeOpacity={0.5}
      onPress={() => onPressCard && onPressCard(title)}
      style={[styles.card, containerStyle]}
    >
      <AppText style={[styles.title, titleStyle]}>{title}</AppText>
      <HorizontalLine
        backgroundColor={colors.grey_20}
        containerStyle={{ width: 110 }}
        marginTop={5}
        marginBottom={12}
      />
      <View style={styles.iconWrapper}>{icon}</View>
      <AppText style={[styles.value, valueStyle]}>
        {value}
        {unit && <AppText style={styles.unit}> {unit}</AppText>}
      </AppText>
      <AppText style={[styles.unit, subtitleStyle]}>{subtitle}</AppText>
    </TouchableOpacity>
  );
};

export default HealthCard;

const styles = StyleSheet.create({
  card: {
    backgroundColor: colors.grey_10,
    borderRadius: 12,
    alignItems: 'center',
    padding: 8,
  },
  title: {
    ...appStyles.h4,
  },
  iconWrapper: {
    marginBottom: 8,
  },
  value: {
    fontSize: 24,
    fontFamily: fonts.NotoSans_Bold,
    color: colors.grey_90,
  },
  unit: {
    fontSize: 10,
    fontFamily: fonts.NotoSans_Medium,
    color: colors.grey_50,
  },
});
