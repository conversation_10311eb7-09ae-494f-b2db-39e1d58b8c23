import React, { useState } from 'react';
import { View, FlatList, StyleSheet } from 'react-native';
import AssessmentSelector from './AssessmentSelector';
import AppText from '../common/AppText';
import AppButton from '../common/AppButton';
import assesmentStyles from './assesmentStyles';

interface Props {
  value: string;
  onChange: (value: string) => void;
  onContinue: () => void;
  onPressBack: () => void;
}

const sleepQuality = [
  'Excellante',
  'Bonne',
  'Assez',
  'Beaucoup',
  'Vraiment beaucoup',
];

const SleepQualityStep: React.FC<Props> = ({
  value,
  onChange,
  onContinue,
  onPressBack,
}) => {
  const [activeValue, setActiveValue] = useState<string>(value);

  return (
    <View style={styles.container}>
      <AppText ml={4} style={assesmentStyles.assessmentTitle}>
        How would you rate the overall quality of your sleep?
      </AppText>

      <View style={{ flex: 1 }}>
        <FlatList
          data={sleepQuality}
          keyExtractor={item => item}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={assesmentStyles.contentContainer}
          renderItem={({ item }) => (
            <AssessmentSelector
              option={item}
              selected={activeValue === item}
              onPress={() => {
                setActiveValue(item);
                onChange(item);
              }}
            />
          )}
          ItemSeparatorComponent={() => <View style={{ height: 12 }} />}
        />
      </View>
      <AppButton title="Continue" onPress={onContinue} disabled={!value} />
      <AppButton
        title="Back"
        containerStyle={assesmentStyles.skipButton}
        titleStyle={assesmentStyles.skipButtonTitle}
        onPress={onPressBack}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default SleepQualityStep;
