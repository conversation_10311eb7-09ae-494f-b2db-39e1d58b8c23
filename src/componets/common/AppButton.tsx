import React from 'react';
import {
  StyleSheet,
  StyleProp,
  ViewStyle,
  ActivityIndicator,
  View,
  TextStyle,
  Text,
  TouchableOpacity,
} from 'react-native';
import { colors, fonts } from '../../theme/theme';

type AppButtonProps = {
  title: string;
  loading?: boolean;
  containerStyle?: StyleProp<ViewStyle>;
  titleStyle?: StyleProp<TextStyle>;
  disabled?: boolean;
  onPress?: () => void;
};

const AppButton = ({
  title,
  loading = false,
  disabled,
  containerStyle,
  titleStyle,
  onPress,
}: AppButtonProps) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled || loading}
      style={[
        styles.button,
        (disabled || loading) && styles.disabledButton,
        containerStyle,
      ]}
      activeOpacity={0.7}
    >
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator color={colors.white} style={{ marginRight: 8 }} />
          <Text numberOfLines={1} style={[styles.text]}>
            Loading...
          </Text>
        </View>
      ) : (
        <Text
          numberOfLines={1}
          style={[
            styles.text,
            { color: disabled ? colors.grey_60 : colors.white },
            titleStyle,
          ]}
        >
          {title}
        </Text>
      )}
    </TouchableOpacity>
  );
};

export default AppButton;

const styles = StyleSheet.create({
  button: {
    height: 46,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.primary,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  text: {
    fontSize: 14,
    fontFamily: fonts.NotoSans_Medium,
  },
  disabledButton: {
    backgroundColor: colors.grey_10,
  },
});
