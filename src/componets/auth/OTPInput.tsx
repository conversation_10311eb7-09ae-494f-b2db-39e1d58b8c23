import React, { useRef, useState } from 'react';
import {
  StyleSheet,
  View,
  TextInput,
  TouchableWithoutFeedback,
} from 'react-native';
import { colors, fonts } from '../../theme/theme';
type OTPInputProps = {
  length?: number;
  onChangeOTP?: (otp: string) => void;
};
// OTPInput component: renders a row of input boxes for OTP entry
const OTPInput = ({ length = 4, onChangeOTP }: OTPInputProps) => {
  // Initialize OTP state with empty strings of specified length
  const [otp, setOtp] = useState<string[]>(new Array(length).fill(''));

  // Track currently focused input index to apply styles
  const [focusedIndex, setFocusedIndex] = useState<number | null>(null);

  // Refs to hold references to each TextInput for focus control
  const inputs = useRef<Array<TextInput | null>>([]);

  // Handle text input in each box
  const handleChange = (text: string, index: number) => {
    const newOtp = [...otp];
    newOtp[index] = text.replace(/[^0-9]/g, '');
    setOtp(newOtp);

    // ✅ Call the parent function with the joined OTP
    onChangeOTP?.(newOtp.join(''));

    if (text && index < length - 1) {
      inputs.current[index + 1]?.focus();
    }
  };

  // Handle backspace key to move focus to previous field if empty
  const handleKeyPress = (e: any, index: number) => {
    if (e.nativeEvent.key === 'Backspace' && !otp[index] && index > 0) {
      inputs.current[index - 1]?.focus();
    }
  };

  return (
    <View style={styles.row}>
      {otp.map((digit, index) => {
        const isFocused = focusedIndex === index;

        return (
          <TouchableWithoutFeedback
            key={index}
            onPress={() => inputs.current[index]?.focus()} // Focus input when box tapped
          >
            <View
              style={[
                styles.outerBox,
                {
                  borderColor: isFocused ? '#D1E6FF' : colors.background_color,
                },
              ]}
            >
              <View
                style={[
                  styles.inputWrapper,
                  {
                    borderColor: isFocused ? '#0873B4' : colors.grey_05,
                  },
                ]}
              >
                <TextInput
                  ref={ref => {
                    inputs.current[index] = ref; // Assign ref
                  }}
                  value={digit}
                  onChangeText={text => handleChange(text, index)}
                  onKeyPress={e => handleKeyPress(e, index)}
                  onFocus={() => setFocusedIndex(index)} // Track focus
                  onBlur={() => setFocusedIndex(null)} // Reset on blur
                  maxLength={1} // Single digit
                  keyboardType="number-pad"
                  style={styles.input}
                  placeholder="_"
                  placeholderTextColor={'#AEAEB2'}
                  cursorColor={'#AEAEB2'}
                  textAlign="center"
                  textAlignVertical="center"
                />
              </View>
            </View>
          </TouchableWithoutFeedback>
        );
      })}
    </View>
  );
};

export default OTPInput;

// Styles
const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 32,
  },
  outerBox: {
    width: 58,
    height: 58,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
  },
  inputWrapper: {
    width: 54,
    height: 54,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.grey_05, // Default border color
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background_color,
  },

  input: {
    fontSize: 20,
    fontWeight: '500',
    color: '#1A1A1A',
    fontFamily: fonts.NotoSans_Medium,
    textAlign: 'center',
  },
});
