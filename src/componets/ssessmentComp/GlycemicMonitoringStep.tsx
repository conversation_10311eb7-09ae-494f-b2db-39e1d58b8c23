import { StyleSheet, View } from 'react-native';
import React from 'react';
import AppText from '../common/AppText';
import assesmentStyles from './assesmentStyles';
import AppButton from '../common/AppButton';
import FormInput from '../common/FormInput';
import ExpandablePicker from '../common/ExpandablePicker';
import CustomCheckbox from '../common/CustomCheckbox';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

export interface GlycemicMonitoringData {
  hba1c: string;
  fastingGlucose: string;
  lastTestInterval: string;
  hba1cNotRemembered: boolean;
  lastTestMoreThanYear: boolean;
}

interface Props {
  value: GlycemicMonitoringData;
  onChange: (value: GlycemicMonitoringData) => void;
  onContinue: () => void;
  onPressBack: () => void;
}

const options = ['2 months', '4 months', '6 months'];

const GlycemicMonitoringStep: React.FC<Props> = ({
  value,
  onChange,
  onContinue,
  onPressBack,
}) => {
  const handleChange = (key: keyof GlycemicMonitoringData, val: any) => {
    onChange({ ...value, [key]: val });
  };
  console.log('value-------', value);

  return (
    <View style={styles.container}>
      <KeyboardAwareScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        <AppText style={assesmentStyles.assessmentTitle}>
          What was your most recent HbA1c?{' '}
          <AppText style={assesmentStyles.paragraph}>(in %)</AppText>
        </AppText>
        <FormInput
          value={value.hba1c}
          onChangeText={text => handleChange('hba1c', text)}
          innerContainer={assesmentStyles.innerContainer}
          containerStyle={{ marginTop: 4 }}
        />

        <CustomCheckbox
          checked={value.hba1cNotRemembered}
          onToggle={() =>
            handleChange('hba1cNotRemembered', !value.hba1cNotRemembered)
          }
          label="I don’t remember my HbA1c"
          containerStyle={{ marginTop: 16 }}
        />

        <AppText pt={30} mb={6} style={assesmentStyles.assessmentTitle}>
          A quand remonte votre dernier test?
        </AppText>
        <ExpandablePicker
          data={options}
          selectedValue={value.lastTestInterval}
          onValueChange={item => handleChange('lastTestInterval', item)}
          placeholder="Select value"
        />

        <CustomCheckbox
          checked={value.lastTestMoreThanYear}
          onToggle={() =>
            handleChange('lastTestMoreThanYear', !value.lastTestMoreThanYear)
          }
          label="More than year"
          containerStyle={{ marginTop: 16 }}
        />

        <AppText pt={30} style={assesmentStyles.assessmentTitle}>
          What was your most recent fasting blood glucose reading?
          <AppText style={assesmentStyles.paragraph}>(mg/dL)</AppText>
        </AppText>
        <FormInput
          value={value.fastingGlucose}
          onChangeText={text => handleChange('fastingGlucose', text)}
          innerContainer={assesmentStyles.innerContainer}
          containerStyle={{ marginTop: 4 }}
        />
      </KeyboardAwareScrollView>

      <View style={{ paddingTop: 16 }}>
        <AppButton
          title="Continue"
          onPress={onContinue}
          disabled={
            !value.hba1c.length &&
            !value.lastTestMoreThanYear &&
            !value.fastingGlucose.length
          }
        />
        <AppButton
          title="Back"
          containerStyle={[assesmentStyles.skipButton, { marginBottom: 32 }]}
          titleStyle={assesmentStyles.skipButtonTitle}
          onPress={onPressBack}
        />
      </View>
    </View>
  );
};

export default GlycemicMonitoringStep;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
  },
  scrollContent: {
    paddingBottom: 48,
  },
});
