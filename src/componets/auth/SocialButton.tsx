import React from 'react';
import {
  StyleSheet,
  TouchableOpacity,
  View,
  StyleProp,
  TextStyle,
  GestureResponderEvent,
  ViewStyle,
} from 'react-native';
import AppText from '../common/AppText';
import { colors, fonts } from '../../theme/theme';
import { VerticalLine } from '../../assets/svgs';

interface SocialButtonProps {
  onPress?: (event: GestureResponderEvent) => void;
  title: string;
  titleStyle?: StyleProp<TextStyle>;
  icon: React.ReactNode;
  containerStyle?: ViewStyle;
}

const SocialButton: React.FC<SocialButtonProps> = ({
  onPress,
  title,
  titleStyle,
  icon,
  containerStyle,
}) => {
  return (
    <TouchableOpacity
      activeOpacity={0.5}
      style={[styles.button, containerStyle]}
      onPress={onPress}
    >
      <View style={styles.logoContainer}>
        {icon}
        <VerticalLine />
      </View>
      <AppText style={[styles.title, titleStyle]}>{title}</AppText>
      <View style={styles.spacer} />
    </TouchableOpacity>
  );
};

export default SocialButton;

const styles = StyleSheet.create({
  button: {
    height: 46,
    borderWidth: 1,
    borderColor: colors.grey_20,
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
    borderRadius: 14,
    backgroundColor: colors.background_color,
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,

    elevation: 1,
  },
  logoContainer: {
    width: 45,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  spacer: {
    width: 45,
  },
  title: {
    fontSize: 14,
    color: colors.grey_30,
    fontFamily: fonts.NotoSans_Medium,
  },
});
