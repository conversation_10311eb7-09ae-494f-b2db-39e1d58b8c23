import React from 'react';
import { ScrollView, StyleSheet, Text, View } from 'react-native';
import { AppText, HorizontalLine, SettingItem } from '../../../componets';
import { colors, fonts, sizes } from '../../../theme/theme';
import { PolicyStroke } from '../../../assets/svgs';

const About = () => {
  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.content}
      showsVerticalScrollIndicator={false}
    >
      {/* Welcome Section */}
      <AppText style={styles.paragraph}>
        Welcome to VitaeChek, your all-in-one healthcare management app designed
        to make health and wellness accessible, efficient, and stress-free.
        Whether you're a patient looking for better ways to manage your health
        or a doctor seeking a tool to enhance patient care, <PERSON>eChek is here to
        transform your healthcare experience.
      </AppText>
      {/*   OUR MISSION */}
      <AppText pt={24} style={styles.title}>
        Our Mission
      </AppText>
      <HorizontalLine marginTop={2} backgroundColor={colors.grey_20} />

      <AppText pt={10} style={styles.paragraph}>
        At VitaeChek, we aim to bridge the gap between patients and healthcare
        providers by leveraging technology to deliver seamless communication,
        reliable health tracking, and efficient care management. We believe that
        better health starts with better tools.
      </AppText>
      {/* WHAT WE OFFER */}
      <AppText pt={24} style={styles.title}>
        What We Offer
      </AppText>
      <HorizontalLine marginTop={2} backgroundColor={colors.grey_20} />

      <AppText pt={10} style={styles.paragraph}>
        <Text style={styles.bold}>For Patients:</Text> Stay on top of your
        health with features like appointment scheduling, health monitoring, and
        secure communication with doctors.{'\n'}
        <Text style={styles.bold}>For Doctors:</Text> Manage patient records,
        track appointments, and provide care with greater efficiency.
      </AppText>
      {/* Why Choose VitaeChek */}
      <AppText pt={24} style={styles.title}>
        Why Choose VitaeChek?
      </AppText>
      <HorizontalLine
        marginBottom={10}
        marginTop={2}
        backgroundColor={colors.grey_20}
      />
      <BulletItem text="Simplify healthcare management." />
      <BulletItem text="Empower yourself with tools to track and improve your health." />
      <BulletItem text="Build stronger connections with healthcare providers." />
      {/* Share Vitaechek */}
      <SettingItem
        label="Share Vitaechek"
        icon={<PolicyStroke />}
        containerStyle={{ marginTop: 4 }}
      />
    </ScrollView>
  );
};

export default About;

// Reusable bullet point
const BulletItem = ({ text }: { text: string }) => (
  <View style={styles.bulletItem}>
    <Text style={styles.bulletPoint}>{'\u2022'}</Text>
    <Text style={styles.paragraph}>{text}</Text>
  </View>
);

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
  },
  content: {
    padding: sizes.paddingHorizontal,
    paddingTop: 24,
    paddingBottom: 32,
  },
  title: {
    fontSize: 18,
    color: colors.grey_90,
    fontFamily: fonts.Catamaran_SemiBold,
  },
  paragraph: {
    fontSize: 12,
    lineHeight: 22,
    color: colors.grey_80,
    fontFamily: fonts.NotoSans_Regular,
  },
  bold: {
    fontFamily: fonts.NotoSans_Bold,
    color: colors.grey_90,
  },
  sectionHeader: {
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: fonts.Catamaran_Bold,
    color: colors.grey_90,
    marginBottom: 4,
  },
  line: {
    height: StyleSheet.hairlineWidth,
    backgroundColor: colors.grey_30,
    width: '100%',
  },

  bulletItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  bulletPoint: {
    fontSize: 18,
    lineHeight: 22,
    color: colors.grey_80,
    marginRight: 6,
  },
  bulletText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 22,
    fontFamily: fonts.Catamaran_Regular,
    color: colors.grey_80,
  },
});
