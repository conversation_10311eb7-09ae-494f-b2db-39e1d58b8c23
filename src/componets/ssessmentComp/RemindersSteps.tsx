import { StyleSheet, View } from 'react-native';
import React from 'react';
import { colors } from '../../theme/theme';
import AppText from '../common/AppText';
import assesmentStyles from './assesmentStyles';
import ReminderCard from '../common/ReminderCard';
import AppButton from '../common/AppButton';

interface Props {
  onChange: (value: { bloodPressure: boolean }) => void;
  onContinue: () => void;
  onPressBack: () => void;
  value: { bloodPressure: boolean };
}

const RemindersSteps: React.FC<Props> = ({
  onChange,
  onContinue,
  onPressBack,
  value,
}) => {
  return (
    <View style={styles.container}>
      <View style={{ flex: 1 }}>
        <AppText style={assesmentStyles.assessmentTitle}>
          Develop the habit of a winner
        </AppText>
        <ReminderCard
          title="Blood pressure measurement "
          isOn={value.bloodPressure}
          onToggle={val => onChange({ bloodPressure: val })}
          containerStyle={{ marginTop: 12 }}
        />
      </View>

      <AppButton title="Continue" onPress={onContinue} disabled={!value} />
      <AppButton
        title="Back"
        containerStyle={assesmentStyles.skipButton}
        titleStyle={assesmentStyles.skipButtonTitle}
        onPress={onPressBack}
      />
    </View>
  );
};

export default RemindersSteps;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
  },
});
