import { StyleSheet, View } from 'react-native';
import React from 'react';
import AppText from '../common/AppText';
import assesmentStyles from './assesmentStyles';
import AppButton from '../common/AppButton';
import FormInput from '../common/FormInput';
import CustomCheckbox from '../common/CustomCheckbox';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

export interface BloodPressureData {
  systolic: string;
  diastolic: string;
  pulse: string;
  bpNotRemembered: boolean;
}

interface Props {
  value: BloodPressureData;
  onChange: (value: BloodPressureData) => void;
  onContinue: () => void;
  onPressBack: () => void;
}

const BloodPressureStep: React.FC<Props> = ({
  value,
  onChange,
  onContinue,
  onPressBack,
}) => {
  const handleChange = (
    key: keyof BloodPressureData,
    val: string | boolean,
  ) => {
    onChange({ ...value, [key]: val });
  };

  return (
    <View style={styles.container}>
      <KeyboardAwareScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        <AppText style={assesmentStyles.assessmentTitle}>
          What was your average blood pressure measurement?
        </AppText>

        <View style={styles.inputsRow}>
          <View style={styles.inputColumn}>
            <AppText style={assesmentStyles.assessmentTitle}>Sys</AppText>
            <FormInput
              value={value.systolic}
              onChangeText={text => {
                const clean = text.replace(/[^0-9]/g, ''); // allow only digits
                handleChange('systolic', clean);
              }}
              keyboardType="number-pad"
              maxLength={3}
              innerContainer={assesmentStyles.innerContainer}
              containerStyle={styles.inputSpacing}
            />
          </View>

          <View style={styles.inputColumn}>
            <AppText style={assesmentStyles.assessmentTitle}>
              Dia
              <AppText style={assesmentStyles.paragraph}> (mm/Hg)</AppText>
            </AppText>
            <FormInput
              value={value.diastolic}
              onChangeText={text => {
                const clean = text.replace(/[^0-9]/g, '');
                handleChange('diastolic', clean);
              }}
              keyboardType="number-pad"
              maxLength={3}
              innerContainer={assesmentStyles.innerContainer}
              containerStyle={styles.inputSpacing}
            />
          </View>

          <View style={styles.inputColumn}>
            <AppText style={assesmentStyles.assessmentTitle}>Pulse</AppText>
            <FormInput
              value={value.pulse}
              onChangeText={text => {
                const clean = text.replace(/[^0-9]/g, '');
                handleChange('pulse', clean);
              }}
              keyboardType="number-pad"
              maxLength={3}
              innerContainer={assesmentStyles.innerContainer}
              containerStyle={styles.inputSpacing}
            />
          </View>
        </View>

        <CustomCheckbox
          checked={value.bpNotRemembered}
          onToggle={() =>
            handleChange('bpNotRemembered', !value.bpNotRemembered)
          }
          label="I don’t remember my BP"
          containerStyle={styles.checkboxSpacing}
        />
      </KeyboardAwareScrollView>
      <AppButton
        title="Continue"
        onPress={onContinue}
        disabled={
          !value.bpNotRemembered &&
          (!value.systolic || !value.diastolic || !value.pulse)
        }
      />
      <AppButton
        title="Back"
        containerStyle={assesmentStyles.skipButton}
        titleStyle={assesmentStyles.skipButtonTitle}
        onPress={onPressBack}
      />
    </View>
  );
};

export default BloodPressureStep;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
  },
  content: {
    flex: 1,
  },
  inputsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },
  inputColumn: {
    width: '31%',
  },
  inputSpacing: {
    marginTop: 4,
  },
  checkboxSpacing: {
    marginTop: 16,
  },
  scrollContent: {
    paddingBottom: 48,
  },
});
