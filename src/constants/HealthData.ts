import {
  BloodPressureCircle,
  GlucoseCircle,
  HeartCircle,
  StepIconCircle,
  WeightCircle,
} from '../assets/svgs';

export const healthData = [
  {
    title: 'Heart Rate',
    icon: HeartCircle,
    value: 130,
    subtitle: 'Normal 200 bpm',
  },
  {
    title: 'Weight',
    icon: WeightCircle,
    value: 120,
    unit: 'KG',
    subtitle: 'Obese',
  },
  {
    title: 'Blood Pressure',
    icon: BloodPressureCircle,
    value: '128/88',
    subtitle: 'Normal <120 SYS <80 DYS',
  },
  {
    title: 'Blood Glucose',
    icon: GlucoseCircle,
    value: 5.0,
    subtitle: 'Normal 4.5 to 6.6 mmol/L',
  },
  {
    title: 'Steps',
    icon: StepIconCircle,
    value: '1,132',
    subtitle: '50% less than last data',
  },
];
