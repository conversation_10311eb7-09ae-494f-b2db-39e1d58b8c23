import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  TextInput,
  Text,
  Animated,
  StyleSheet,
  TextInputProps,
  Pressable,
  ViewStyle,
  Platform,
  TextStyle,
} from 'react-native';
import { appStyles, colors, fonts } from '../../theme/theme';
import { CrossStroke, Eye, EyeOff } from '../../assets/svgs';
import AppText from './AppText';

interface FormInputProps extends TextInputProps {
  label?: string;
  value: string;
  onChangeText: (text: string) => void;
  error?: string;
  isPassword?: boolean;
  containerStyle?: ViewStyle;
  outerContainer?: ViewStyle;
  innerContainer?: ViewStyle;
  rightIcon?: React.ReactNode;
  titleStyle?: TextStyle;
  title?: string;
}

const FormInput: React.FC<FormInputProps> = ({
  label,
  value,
  onChangeText,
  error,
  isPassword = false,
  secureTextEntry,
  containerStyle,
  outerContainer,
  innerContainer,
  rightIcon,
  titleStyle,
  title,
  ...rest
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [hidePassword, setHidePassword] = useState(true);

  const animation = useRef(new Animated.Value(value ? 1 : 0)).current;

  // Animate label on focus or value change
  useEffect(() => {
    Animated.timing(animation, {
      toValue: isFocused || value ? 1 : 0,
      duration: 150,
      useNativeDriver: false,
    }).start();
  }, [isFocused, value]);

  // Floating label animated styles
  const labelStyle = {
    ...StyleSheet.flatten([
      styles.label,
      {
        top: animation.interpolate({
          inputRange: [0, 1],
          outputRange: [16, 6],
        }),
        fontSize: animation.interpolate({
          inputRange: [0, 1],
          outputRange: [14, 12],
        }),
        color: colors.light_Secondary,
      },
    ]),
  };

  return (
    <View style={[styles.wrapper, containerStyle]}>
      {title && (
        <AppText pb={10} style={[styles.title, titleStyle]}>
          {title}
        </AppText>
      )}
      {/* Outer border layer */}
      <View
        style={[
          styles.outerBorder,
          outerContainer,
          {
            borderColor: error ? '#FEDEDE' : colors.background_color,
          },
        ]}
      >
        {/* Inner input container with label and input */}
        <View
          style={[
            styles.inputContainer,
            innerContainer,
            {
              borderColor: error ? colors.warning : colors.grey_05,
            },
          ]}
        >
          {/* Animated floating label */}
          <Animated.Text style={labelStyle}>{label}</Animated.Text>

          {/* Text input field */}
          <TextInput
            style={[
              styles.input,
              { paddingTop: label ? 18 : Platform.OS === 'android' ? 12 : 0 },
            ]}
            value={value}
            onChangeText={onChangeText}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            secureTextEntry={isPassword && hidePassword}
            {...rest}
          />

          {/* Toggle password visibility */}
          {isPassword ? (
            <Pressable
              style={styles.icon}
              onPress={() => setHidePassword(!hidePassword)}
            >
              {hidePassword ? (
                <Eye />
              ) : (
                <EyeOff stroke="#C3C3C3" width={19} height={19} />
              )}
            </Pressable>
          ) : (
            <Pressable style={styles.icon}>{rightIcon}</Pressable>
          )}
        </View>
      </View>

      {/* Error message display */}
      {!!error && (
        <View style={[appStyles.flexRow, { marginTop: 5, gap: 9 }]}>
          {/* <CrossFill /> */}
          <View style={styles.iconContainer}>
            <CrossStroke width={16} height={16} stroke={colors.white} />
          </View>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    // Container for the full input field (label + input + error)
  },
  title: {
    fontSize: 14,
    color: colors.grey_80,
    fontFamily: fonts.NotoSans_Regular,
    lineHeight: 20,
  },
  outerBorder: {
    borderWidth: 2,
    height: 62,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  inputContainer: {
    height: 58,
    borderWidth: 1,
    borderRadius: 14,
    justifyContent: 'center',
    paddingHorizontal: 16,
    width: '100%',
    alignSelf: 'center',
  },
  input: {
    fontSize: 12,
    color: colors.grey_80,
    paddingRight: 16,
    fontFamily: fonts.NotoSans_Regular,
  },
  label: {
    position: 'absolute',
    left: 16,
    fontFamily: fonts.NotoSans_Regular,
  },
  errorText: {
    color: colors.warning,
    fontSize: 12,
    fontFamily: fonts.NotoSans_Medium,
  },
  icon: {
    position: 'absolute',
    right: 16,
    top: 18,
  },
  iconContainer: {
    width: 16,
    height: 16,
    backgroundColor: colors.warning,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
    marginLeft: 16,
  },
});

export default FormInput;
