import React, { useState, useRef, useEffect } from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  Animated,
  FlatList,
  ViewStyle,
  TextStyle,
  Keyboard,
} from 'react-native';
import { colors, fonts } from '../../theme/theme';
import AppText from './AppText';
import { ArrowDown, ChevronDown } from '../../assets/svgs';

interface DropdownOption {
  label: string;
  value: string;
}

interface Props {
  label: string;
  options: DropdownOption[];
  selectedValue?: string;
  onSelect: (value: string) => void;
  containerStyle?: ViewStyle;
  dropdownStyle?: ViewStyle;
  labelStyle?: TextStyle;
  valueStyle?: TextStyle;
  disabled?: boolean;
  maxHeight?: number; // Maximum height for dropdown options
}

const DropDownPicker: React.FC<Props> = ({
  label,
  options,
  selectedValue,
  onSelect,
  containerStyle,
  dropdownStyle,
  labelStyle,
  valueStyle,
  disabled = false,
  maxHeight = 200,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [animation] = useState(new Animated.Value(58)); // Initial height (dropdown button height)

  // Animations
  const labelAnimation = useRef(
    new Animated.Value(selectedValue ? 1 : 0),
  ).current;
  const rotationAnimation = useRef(new Animated.Value(0)).current;

  const selectedOption = options.find(option => option.value === selectedValue);
  const hasValue = !!selectedValue;

  // Label animation effect - only animate when there's a selected value
  useEffect(() => {
    Animated.timing(labelAnimation, {
      toValue: hasValue ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [hasValue, labelAnimation]);

  // Handle dropdown toggle
  const togglePicker = () => {
    if (disabled) return;

    Keyboard.dismiss();
    const initialHeight = 58; // Dropdown button height
    const itemHeight = 50; // Height per option item
    const padding = 8; // Top padding
    const maxItems = Math.floor((maxHeight - padding) / itemHeight);
    const visibleItems = Math.min(options.length, maxItems);
    const expandedHeight = initialHeight + visibleItems * itemHeight + padding;

    // Animate container height
    Animated.timing(animation, {
      toValue: isOpen ? initialHeight : expandedHeight,
      duration: 300,
      useNativeDriver: false,
    }).start();

    // Animate arrow rotation
    Animated.timing(rotationAnimation, {
      toValue: isOpen ? 0 : 1,
      duration: 300,
      useNativeDriver: true,
    }).start();

    setIsOpen(!isOpen);
    setIsFocused(!isOpen);
  };

  // Handle option selection
  const handleSelect = (value: string) => {
    onSelect(value);
    togglePicker();
  };

  // Animated values for label
  const labelTop = labelAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [16, 4], // Keep label inside container
  });

  const labelFontSize = labelAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [16, 12],
  });

  const labelColor = labelAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [colors.grey_60, colors.grey_60],
  });

  const arrowRotation = rotationAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });

  return (
    <Animated.View
      style={[styles.container, containerStyle, { height: animation }]}
    >
      {/* Main dropdown button */}
      <TouchableOpacity
        style={[
          styles.dropdown,
          dropdownStyle,
          (isFocused || isOpen) && styles.dropdownFocused,
          disabled && styles.dropdownDisabled,
          isOpen && styles.dropdownOpen,
        ]}
        onPress={togglePicker}
        disabled={disabled}
        activeOpacity={0.7}
      >
        {/* Label - shows as default text when no selection, moves up when selected */}
        <Animated.Text
          style={[
            styles.label,
            labelStyle,
            {
              top: labelTop,
              fontSize: labelFontSize,
              color: labelColor,
            },
          ]}
        >
          {label}
        </Animated.Text>

        {/* Selected value - shows below the label */}
        {hasValue && (
          <AppText style={[styles.value, valueStyle]}>
            {selectedOption?.label || selectedValue}
          </AppText>
        )}

        {/* Arrow icon */}
        <Animated.View
          style={[
            styles.iconContainer,
            {
              transform: [{ rotate: arrowRotation }],
            },
          ]}
        >
          <ChevronDown stroke={colors.grey_30} />
        </Animated.View>
      </TouchableOpacity>

      {/* Options dropdown */}
      {isOpen && (
        <FlatList
          data={options}
          keyExtractor={item => item.value}
          renderItem={({ item, index }) => (
            <TouchableOpacity
              style={[
                styles.option,
                item.value === selectedValue && styles.selectedOption,
                index === options.length - 1 && styles.lastOption,
              ]}
              onPress={() => handleSelect(item.value)}
              activeOpacity={0.7}
            >
              <AppText
                style={[
                  styles.optionText,
                  item.value === selectedValue && styles.selectedOptionText,
                ]}
              >
                {item.label}
              </AppText>
            </TouchableOpacity>
          )}
          showsVerticalScrollIndicator={true}
          style={styles.optionsList}
          scrollEnabled={options.length * 50 > maxHeight - 8}
          nestedScrollEnabled={true}
          contentContainerStyle={{ paddingBottom: 10 }}
        />
      )}
    </Animated.View>
  );
};

export default DropDownPicker;

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
    borderRadius: 12,
    justifyContent: 'flex-start',
    backgroundColor: colors.background_color,
    borderWidth: 1,
    borderColor: colors.grey_20,

    overflow: 'hidden',
  },
  dropdown: {
    backgroundColor: 'transparent',
    paddingHorizontal: 16,

    minHeight: 58,
    position: 'relative',
    justifyContent: 'center',
  },
  dropdownFocused: {
    // Focus styles can be applied to container if needed
  },
  dropdownOpen: {
    // Open styles handled by container
  },
  dropdownDisabled: {
    opacity: 0.6,
  },
  label: {
    position: 'absolute',
    left: 16,
    fontFamily: fonts.NotoSans_Regular,
    backgroundColor: 'transparent',
    paddingHorizontal: 0,
    zIndex: 2,
  },
  value: {
    fontSize: 16,
    fontFamily: fonts.NotoSans_Medium,
    color: colors.grey_90,
    lineHeight: 20,
    marginTop: 16, // Space below the label
  },
  placeholder: {
    fontSize: 16,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_50,
    lineHeight: 20,
  },
  iconContainer: {
    position: 'absolute',
    right: 16,
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionsList: {
    marginTop: 10,
  },
  option: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    minHeight: 50,
    justifyContent: 'center',
    borderTopWidth: 1,
    borderTopColor: colors.grey_20,
  },
  lastOption: {
    borderTopWidth: 1,
    borderTopColor: colors.grey_20,
  },
  selectedOption: {
    backgroundColor: colors.cyan_blue,
  },
  optionText: {
    fontSize: 16,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_90,
    lineHeight: 20,
  },
  selectedOptionText: {
    color: colors.primary,
    fontFamily: fonts.NotoSans_Medium,
  },
});
