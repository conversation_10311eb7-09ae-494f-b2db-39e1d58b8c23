import React, { useState } from 'react';
import {
  ScrollView,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
} from 'react-native';
import {
  AppText,
  HorizontalLine,
  CustomCheckbox,
  AppButton,
} from '../../../componets';
import { colors, fonts, sizes } from '../../../theme/theme';

const TermsAndConditions = () => {
  const [agreed, setAgreed] = useState(false);

  const handleAgreeToggle = () => {
    setAgreed(!agreed);
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <AppText style={styles.lastUpdated}>
          Last Updated: 12th June, 2024
        </AppText>
      </View>

      {/* Welcome Section */}
      <AppText style={styles.welcomeText}>
        Welcome to VitaeChek! By using our app, you agree to the following terms
        and conditions. Please read them carefully.
      </AppText>

      {/* Main Content */}
      <AppText style={styles.mainContent}>
        By using VitaeChek, you agree to provide accurate information, use the
        app responsibly, and comply with laws. VitaeChek supports health
        management but is not a substitute for medical advice. Unauthorized
        activities are prohibited, and data is processed per our Privacy Policy.
        Use is at your own risk, and terms may change periodically.
      </AppText>

      {/* Agreement Section */}
      <View style={styles.agreementSection}>
        <CustomCheckbox
          checked={agreed}
          onToggle={handleAgreeToggle}
          label=""
          fillCheck={{ width: 20, height: 20 }}
          strokeCheck={{ width: 20, height: 20 }}
          containerStyle={styles.checkboxContainer}
        />
        <TouchableOpacity
          onPress={handleAgreeToggle}
          style={styles.agreementTextContainer}
        >
          <AppText style={styles.agreementText}>
            I agree to the{' '}
            <Text style={styles.linkText}>terms & conditions</Text>
          </AppText>
        </TouchableOpacity>
      </View>

      {/* Action Button - Optional for future use */}
      {/* {agreed && (
        <AppButton
          title="Continue"
          onPress={() => {
            // Handle continue action - can be customized based on use case
            console.log('User agreed to terms and conditions');
          }}
          containerStyle={styles.continueButton}
        />
      )} */}
    </View>
  );
};

export default TermsAndConditions;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
    padding: sizes.paddingHorizontal,
  },

  header: {
    marginBottom: 16,
  },
  lastUpdated: {
    fontSize: 12,
    color: colors.grey_60,
    fontFamily: fonts.NotoSans_Regular,
    textAlign: 'left',
  },
  welcomeText: {
    fontSize: 12,
    lineHeight: 22,
    color: colors.grey_80,
    fontFamily: fonts.NotoSans_Regular,
    marginBottom: 16,
  },
  mainContent: {
    fontSize: 12,
    lineHeight: 20,
    color: colors.grey_80,
    fontFamily: fonts.NotoSans_Regular,
    marginBottom: 24,
  },

  agreementSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 25,
    marginBottom: 16,
  },
  checkboxContainer: {
    marginTop: 2,
  },
  agreementTextContainer: {
    flex: 1,
  },
  agreementText: {
    fontSize: 14,
    lineHeight: 22,
    color: colors.grey_80,
    fontFamily: fonts.NotoSans_Regular,
  },
  linkText: {
    color: colors.primary,

    fontFamily: fonts.NotoSans_Medium,
  },
  continueButton: {
    marginTop: 20,
    marginBottom: 16,
  },
});
