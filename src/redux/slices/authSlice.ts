import { IUser as IUserState } from './../../interfaces/IUser';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface iInitialState {
  user: IUserState | null;
}

const initialState: iInitialState = {
  user: null,
};

export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUser: (state, { payload }: PayloadAction<IUserState>) => {
      state.user = { ...payload };
    },
    logoutUser: () => initialState,
    updateUser: (state, { payload }: PayloadAction<Partial<IUserState>>) => {
      if (state.user) {
        state.user = {
          ...state.user,
          ...payload,
        } as IUserState;
      }
    },
  },
});

export const { setUser, logoutUser, updateUser } = userSlice.actions;

export default userSlice;
