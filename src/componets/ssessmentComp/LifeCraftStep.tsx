import { StyleSheet, View } from 'react-native';
import React, { useState, useEffect } from 'react';
import { colors } from '../../theme/theme';
import AppText from '../common/AppText';
import assesmentStyles from './assesmentStyles';
import CustomCheckbox from '../common/CustomCheckbox';
import AppButton from '../common/AppButton';

interface Props {
  onChange: (value: { analyzing: boolean; plan: boolean }) => void;
  onContinue: () => void;
  onPressBack: () => void;
  value: { analyzing: boolean; plan: boolean };
}

const LifeCraftStep: React.FC<Props> = ({
  onChange,
  onContinue,
  onPressBack,
  value,
}) => {
  const [checkboxes, setCheckboxes] = useState({
    analyzing: value?.analyzing,
    plan: value?.plan,
  });

  // Notify parent of state changes
  useEffect(() => {
    onChange(checkboxes);
  }, [checkboxes]);

  const toggleCheckbox = (key: keyof typeof checkboxes) => {
    setCheckboxes(prev => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  return (
    <View style={styles.container}>
      <View style={{ flex: 1 }}>
        <AppText style={assesmentStyles.assessmentTitle}>
          Get ready for a remarkable improvement in your lifestyle.
        </AppText>

        <CustomCheckbox
          label="Analyzing your answers"
          checked={checkboxes.analyzing}
          onToggle={() => toggleCheckbox('analyzing')}
          fillCheck={{ width: 18, height: 18 }}
          strokeCheck={{ width: 18, height: 18 }}
          containerStyle={{ marginTop: 12 }}
          labelStyles={{ color: colors.black }}
        />
        <CustomCheckbox
          label="Create a plan for you start today"
          checked={checkboxes.plan}
          onToggle={() => toggleCheckbox('plan')}
          fillCheck={{ width: 18, height: 18 }}
          strokeCheck={{ width: 18, height: 18 }}
          containerStyle={{ marginTop: 12 }}
          labelStyles={{ color: colors.black }}
        />
      </View>

      <AppButton title="Continue" onPress={onContinue} disabled={!value} />
      <AppButton
        title="Back"
        containerStyle={assesmentStyles.skipButton}
        titleStyle={assesmentStyles.skipButtonTitle}
        onPress={onPressBack}
      />
    </View>
  );
};

export default LifeCraftStep;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
  },
});
