import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  LayoutAnimation,
  Platform,
  UIManager,
  StyleSheet,
  Animated,
} from 'react-native';
import { PieChart } from 'react-native-gifted-charts';
import { appStyles, colors, fonts, sizes } from '../../theme/theme';
import AppText from '../common/AppText';
import { ChevronDown, ChevronUp } from '../../assets/svgs';

// Enable layout animation on Android
if (Platform.OS === 'android') {
  UIManager.setLayoutAnimationEnabledExperimental?.(true);
}

// Static PieChart data for blood glucose fluctuations
const pieData = [
  {
    value: 47,
    color: '#8ED4FF',
    gradientCenterColor: '#0873B4',
    focused: true,
    status: 'Normal',
  },
  {
    value: 40,
    color: '#FC5A5A',
    gradientCenterColor: '#FC5A5A',
    status: 'Diabetes',
  },
  {
    value: 16,
    color: '#FDB022',
    gradientCenterColor: '#FDB022',
    status: 'Pre-Diabeted',
  },
];

const EstimatedA1C = () => {
  const [fluctuations, setFluctuations] = useState<string>('Global');
  const [expanded, setExpanded] = useState(false); // Toggle detailed view
  const opacity = useRef(new Animated.Value(0)).current;
  const scale = useRef(new Animated.Value(0.9)).current;

  // Handle toggle with animation
  const toggleExpand = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setExpanded(prev => !prev);
  };

  // Animate extra content when expanded
  useEffect(() => {
    if (expanded) {
      Animated.parallel([
        Animated.timing(opacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scale, {
          toValue: 1,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      opacity.setValue(0);
      scale.setValue(0.9);
    }
  }, [expanded]);

  return (
    <View style={styles.container}>
      {/* Header Section */}
      <View style={styles.header}>
        <View style={styles.rowStart}>
          <AppText style={styles.title}>Est. A1c</AppText>
          <Text style={styles.a1cTag}>5.91%</Text>
        </View>
        <View style={styles.rowEnd}>
          <Text style={styles.subTitle}>5 Days</Text>
          <ChevronDown stroke={colors.primary} />
        </View>
      </View>

      {/* Label Row */}
      <View style={styles.mgContainerLabel}>
        <Text style={styles.cellValue}>Avg</Text>
        <Text style={styles.cellValue}>Max</Text>
        <Text style={styles.cellValue}>Min</Text>
        <Text style={styles.cellValue}>stdDev</Text>
      </View>

      {/* Blood Glucose Summary Row */}
      <View style={styles.mgRow}>
        <AppText style={styles.mgText}>mg/DL</AppText>
        <View style={styles.mgContainer}>
          {Array(4)
            .fill(0)
            .map((_, index) => (
              <Text key={index} style={styles.cellLabel}>
                146
              </Text>
            ))}
        </View>
      </View>

      {/* Expanded Section with Pie Chart */}
      {expanded && (
        <Animated.View
          style={[
            styles.extraContent,
            {
              opacity,
              transform: [{ scale }],
            },
          ]}
        >
          <Text style={styles.fluctuations}>Fluctuations</Text>
          {/* Fluctuations Categories*/}
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 16 }}>
            {['Global', 'Reveil', 'Breakfast'].map((item, index) => (
              <TouchableOpacity
                onPress={() => setFluctuations(item)}
                key={item}
              >
                <AppText
                  style={[
                    styles.fluctuationsCategory,
                    {
                      color:
                        fluctuations == item ? colors.primary : colors.grey_30,
                    },
                  ]}
                >
                  {item}
                </AppText>
              </TouchableOpacity>
            ))}
          </View>
          {/* PIE CHART */}
          <View style={styles.pieContainer}>
            <PieChart
              data={pieData}
              donut
              showGradient
              sectionAutoFocus
              radius={60}
              innerRadius={30}
              innerCircleColor={colors.white}
            />
            <View style={styles.legend}>
              {pieData.map((item, index) => (
                <View key={index} style={styles.legendItem}>
                  <View
                    style={[styles.legendDot, { backgroundColor: item.color }]}
                  />
                  <Text style={[styles.legendText, styles.legendStatus]}>
                    {item.status}
                  </Text>
                  <Text style={styles.legendText}>{index + 1}x</Text>
                  <Text style={styles.legendText}>
                    {index % 2 === 0 ? '60%' : '30%'}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        </Animated.View>
      )}

      {/* Toggle Button */}
      <TouchableOpacity
        hitSlop={20}
        style={{
          flexDirection: 'row',
          alignSelf: 'center',
          marginTop: 12,
          gap: 8,
        }}
        onPress={toggleExpand}
      >
        <Text style={styles.toggleText}>
          {expanded ? 'See less ' : 'Show more '}
        </Text>
        {expanded ? <ChevronDown stroke={colors.primary} /> : <ChevronUp />}
      </TouchableOpacity>
    </View>
  );
};

export default EstimatedA1C;
const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    marginHorizontal: sizes.paddingHorizontal,
    padding: 8,
    paddingVertical: 10,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,
    elevation: 1,
    marginTop: 34,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  rowStart: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rowEnd: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  title: {
    ...appStyles.h3,
    lineHeight: 15,
  },
  a1cTag: {
    backgroundColor: '#FFE6B5',
    color: '#F79009',
    fontSize: 10,
    fontFamily: fonts.NotoSans_SemiBold,
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 4,
    marginLeft: 14,
    borderWidth: 1,
    borderColor: '#F79009',
  },
  subTitle: {
    fontSize: 12,
    color: colors.primary,
    fontFamily: fonts.NotoSans_Medium,
  },
  mgContainerLabel: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 62,
    justifyContent: 'space-between',
    flex: 1,
    marginTop: 12,
    paddingHorizontal: 6,
  },
  mgRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  mgText: {
    fontSize: 14,
    color: colors.grey_90,
    fontFamily: fonts.NotoSans_Medium,
    lineHeight: 15,
  },
  mgContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 30,
    justifyContent: 'space-between',
    flex: 1,
    backgroundColor: '#FEF0C7',
    marginLeft: 16,
    borderRadius: 4,
    paddingHorizontal: 6,
    marginTop: 4,
  },
  cellLabel: {
    fontSize: 18,
    color: '#F79009',
    fontFamily: fonts.NotoSans_SemiBold,
    lineHeight: 16,
  },
  cellValue: {
    fontSize: 12,
    color: colors.grey_30,
    fontFamily: fonts.NotoSans_Regular,
  },
  extraContent: {
    marginTop: 16,
  },
  fluctuations: {
    ...appStyles.h3,
  },
  pieContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    height: 132,
  },
  legend: {
    marginLeft: 12,
    justifyContent: 'center',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    gap: 7,
  },
  legendDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  legendText: {
    fontSize: 10,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_30,
  },
  legendStatus: {
    width: 70,
  },
  toggleText: {
    color: colors.primary,
    textAlign: 'center',
    fontSize: 12,
    fontFamily: fonts.NotoSans_Medium,
    lineHeight: 12,
  },
  fluctuationsCategory: {
    fontSize: 12,
    color: colors.primary,
    fontFamily: fonts.NotoSans_Regular,
    marginTop: 8,
  },
});
