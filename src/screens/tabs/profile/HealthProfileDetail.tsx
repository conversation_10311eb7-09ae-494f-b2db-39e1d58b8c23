import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../../navigations/StackNavigator';
import { colors, fonts, sizes } from '../../../theme/theme';
import { AppText, DropDownPicker } from '../../../componets';
import HealthProfileCard from '../../../componets/profile/HealthProfileCard';
import ToggleableTag from '../../../componets/common/ToggleableTag';

type Props = NativeStackScreenProps<StackParamList, 'HealthProfileDetail'>;

interface ConditionOption {
  id: string;
  label: string;
  selected: boolean;
}

interface DropdownData {
  label: string;
  value: string;
}

const HealthProfileDetail: React.FC<Props> = ({ navigation, route }) => {
  const { title } = route.params;

  // Generic state for radio options
  const [radioOptions, setRadioOptions] = useState<ConditionOption[]>([]);

  // Generic state for dropdowns
  const [dropdowns, setDropdowns] = useState<DropdownData[]>([]);
  const [dropdownValues, setDropdownValues] = useState<{
    [key: string]: string;
  }>({});

  // Generic state for tag sections
  const [tagSections, setTagSections] = useState<{ [key: string]: string[] }>(
    {},
  );
  const [selectedTags, setSelectedTags] = useState<{ [key: string]: string[] }>(
    {},
  );

  // Initialize data based on the title
  React.useEffect(() => {
    initializeData();
  }, [title]);

  const initializeData = () => {
    switch (title) {
      case 'My condition':
        setRadioOptions([
          { id: 'diabetes', label: 'Diabetes', selected: false },
          { id: 'hypertension', label: 'Hypertension', selected: true },
          { id: 'weight', label: 'Weight management', selected: false },
        ]);
        setDropdowns([
          { label: 'Diabetes type', value: 'Gestational' },
          { label: 'Hypertension', value: 'Gestational' },
        ]);
        setDropdownValues({
          'Diabetes type': 'gestational',
          Hypertension: 'gestational',
        });
        setTagSections({
          'Other Conditions': [
            'None',
            'Hgh lipids',
            'Chronic kidney',
            'Chronic pain',
            'physical disability',
            'cancer',
            'Cardiovascular',
            'Heart problem',
          ],
        });
        setSelectedTags({
          'Other Conditions': ['Hgh lipids'],
        });
        break;

      case 'Meds & supplements':
        setRadioOptions([
          { id: 'pen', label: 'Pen/Syringe', selected: false },
          { id: 'pump', label: 'Pump', selected: true },
          { id: 'none', label: 'None', selected: false },
        ]);
        setDropdowns([
          { label: 'Bolus(fast-acting)', value: 'Humalog' },
          { label: 'Basal(Long-acting)', value: 'NoVo Rapid' },
        ]);
        setDropdownValues({
          'Bolus(fast-acting)': 'humalog',
          'Basal(Long-acting)': 'novorapid',
        });
        setTagSections({
          'Oral medications': [
            'None',
            'High lipids',
            'Chronic kidney d',
            'Chronic pain',
            'physical disability',
            'Cancer',
            'Cardiovascular',
          ],
          Supplements: [
            'None',
            'High lipids',
            'Chronic kidney d',
            'Chronic pain',
            'physical disability',
            'Cancer',
            'Cardiovascular',
          ],
        });
        setSelectedTags({
          'Oral medications': ['High lipids'],
          Supplements: ['High lipids'],
        });
        break;

      case 'Lifestyle':
        setRadioOptions([]);
        setDropdowns([]);
        setDropdownValues({});
        setTagSections({
          'Physical Activity': [
            'Walking',
            'Running',
            'Swimming',
            'Cycling',
            'Yoga',
            'Weight training',
            'Dancing',
            'Sports',
          ],
          'Diet & Nutrition': [
            'Mediterranean',
            'Low carb',
            'Vegetarian',
            'Vegan',
            'Keto',
            'Paleo',
            'Intermittent fasting',
            'No specific diet',
          ],
          'Sleep Pattern': [
            'Less than 6 hours',
            '6-7 hours',
            '7-8 hours',
            '8-9 hours',
            'More than 9 hours',
          ],
          'Stress Management': [
            'Meditation',
            'Deep breathing',
            'Exercise',
            'Music',
            'Reading',
            'Socializing',
            'Hobbies',
            'Professional help',
          ],
        });
        setSelectedTags({
          'Physical Activity': ['Walking', 'Swimming'],
          'Diet & Nutrition': ['Mediterranean'],
          'Sleep Pattern': ['7-8 hours'],
          'Stress Management': [],
        });
        break;
    }
  };

  const toggleRadioOption = (id: string) => {
    setRadioOptions(prev =>
      prev.map(option =>
        option.id === id ? { ...option, selected: !option.selected } : option,
      ),
    );
  };

  const toggleTag = (sectionKey: string, tag: string) => {
    setSelectedTags(prev => ({
      ...prev,
      [sectionKey]: prev[sectionKey]?.includes(tag)
        ? prev[sectionKey].filter(t => t !== tag)
        : [...(prev[sectionKey] || []), tag],
    }));
  };

  const handleDropdownChange = (label: string, value: string) => {
    setDropdownValues(prev => ({
      ...prev,
      [label]: value,
    }));
  };

  const getDropdownOptions = (label: string) => {
    switch (label) {
      case 'Diabetes type':
        return [
          { label: 'Type 1', value: 'type1' },
          { label: 'Type 2', value: 'type2' },
          { label: 'Gestational', value: 'gestational' },
          { label: 'Pre-diabetes', value: 'prediabetes' },
        ];
      case 'Hypertension':
        return [
          { label: 'Primary', value: 'primary' },
          { label: 'Secondary', value: 'secondary' },
          { label: 'Gestational', value: 'gestational' },
          { label: 'White coat', value: 'whitecoat' },
        ];
      case 'Bolus(fast-acting)':
        return [
          { label: 'Humalog', value: 'humalog' },
          { label: 'NovoLog', value: 'novolog' },
          { label: 'Apidra', value: 'apidra' },
          { label: 'Fiasp', value: 'fiasp' },
        ];
      case 'Basal(Long-acting)':
        return [
          { label: 'NoVo Rapid', value: 'novorapid' },
          { label: 'Lantus', value: 'lantus' },
          { label: 'Levemir', value: 'levemir' },
          { label: 'Tresiba', value: 'tresiba' },
        ];
      default:
        return [];
    }
  };

  const getAddButtonText = (sectionKey: string) => {
    switch (sectionKey) {
      case 'Other Conditions':
        return '+ Personnalise';
      case 'Oral medications':
      case 'Supplements':
        return '+ Add New Med';
      case 'Physical Activity':
        return '+ Add Custom';
      case 'Stress Management':
        return '+ Add Method';
      default:
        return '+ Add';
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Header card */}
        <HealthProfileCard title={title} />

        {/* Radio options */}
        {radioOptions.length > 0 && (
          <View style={styles.radioContainer}>
            {radioOptions.map(option => (
              <TouchableOpacity
                key={option.id}
                style={styles.radioRow}
                onPress={() => toggleRadioOption(option.id)}
              >
                <View
                  style={[
                    styles.radioButton,
                    option.selected && styles.radioButtonSelected,
                  ]}
                >
                  {option.selected && <View style={styles.radioButtonInner} />}
                </View>
                <AppText style={styles.radioLabel}>{option.label}</AppText>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Dropdowns */}
        {dropdowns.map((dropdown, index) => (
          <DropDownPicker
            key={index}
            label={dropdown.label}
            options={getDropdownOptions(dropdown.label)}
            selectedValue={dropdownValues[dropdown.label]}
            onSelect={value => handleDropdownChange(dropdown.label, value)}
            containerStyle={styles.dropdownContainer}
          />
        ))}

        {/* Tag sections */}
        {Object.entries(tagSections).map(([sectionKey, tags]) => (
          <View key={sectionKey} style={styles.sectionContainer}>
            <AppText style={styles.sectionTitle}>{sectionKey}</AppText>
            <View style={styles.tagsContainer}>
              {tags.map((tag, index) => (
                <ToggleableTag
                  key={index}
                  item={tag}
                  selected={selectedTags[sectionKey]?.includes(tag)}
                  onPress={() => toggleTag(sectionKey, tag)}
                  showCross={false}
                  tagContainerStyle={styles.tag}
                />
              ))}
              <TouchableOpacity style={styles.addButton}>
                <AppText style={styles.addButtonText}>
                  {getAddButtonText(sectionKey)}
                </AppText>
              </TouchableOpacity>
            </View>
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

export default HealthProfileDetail;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 18,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  radioContainer: {
    marginTop: 20,
  },
  radioRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.grey_50,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonSelected: {
    borderColor: colors.primary,
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: colors.primary,
  },
  radioLabel: {
    fontSize: 16,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_90,
  },
  dropdownContainer: {
    marginTop: 16,
  },
  sectionContainer: {
    marginTop: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: fonts.NotoSans_SemiBold,
    color: colors.grey_90,
    marginBottom: 16,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
  },
  tag: {
    marginRight: 0,
    marginBottom: 8,
  },
  tagSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  tagText: {
    fontSize: 14,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_80,
  },
  tagTextSelected: {
    color: colors.white,
  },
  addButton: {
    backgroundColor: 'transparent',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: colors.grey_50,
    borderStyle: 'dashed',
  },
  addButtonText: {
    fontSize: 14,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_60,
  },
});
