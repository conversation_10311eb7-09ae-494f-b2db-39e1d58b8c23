import {
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React from 'react';
import AppText from '../common/AppText';
import assesmentStyles from './assesmentStyles';
import AppButton from '../common/AppButton';
import ToggleableTag from '../common/ToggleableTag';

interface Props {
  value: string[]; // Selected tags
  onChange: (val: string[]) => void; // Called when tags are toggled
  onContinue?: () => void;
  onPressBack?: () => void;
}
const ComorbidityStep: React.FC<Props> = ({
  value,
  onChange,
  onContinue,
  onPressBack,
}) => {
  const initialTags = [
    'Diabetes',
    'Arthritis',
    'Asthma',
    'Depression',
    'Pet Dandder',
    'Soy',
    'Anxity',
    'OCD',
    'Stroke',
    'Heart Failure',
    'Cancer',
    'Alzheimer',
    'GERD',
    'COPD',
    'Hight Blood Pressure',
    'Peanuts',
    'Trees',
    'High Cholesterol',
    'Thyroid',
    'Carpal Tunnel',
  ];

  const toggleTag = (tag: string) => {
    const updated = value.includes(tag)
      ? value.filter(t => t !== tag)
      : [...value, tag];
    onChange(updated);
  };

  const renderTag = ({ item }: { item: string }) => (
    <ToggleableTag
      item={item}
      selected={value.includes(item)}
      onPress={() => toggleTag(item)}
    />
  );

  return (
    <View style={styles.container}>
      <AppText mb={13} style={assesmentStyles.assessmentTitle}>
        What other health conditions are you managing!
      </AppText>
      <View style={{ flex: 1 }}>
        <FlatList
          data={initialTags}
          renderItem={renderTag}
          keyExtractor={item => item}
          numColumns={3}
          columnWrapperStyle={styles.row}
          contentContainerStyle={styles.list}
        />
      </View>

      <AppButton
        title="Continue"
        onPress={onContinue}
        disabled={!value.length}
      />
      <AppButton
        title="Back"
        containerStyle={assesmentStyles.skipButton}
        titleStyle={assesmentStyles.skipButtonTitle}
        onPress={onPressBack}
      />
    </View>
  );
};

export default ComorbidityStep;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  list: {
    justifyContent: 'flex-start',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    flexWrap: 'wrap',
  },
});
