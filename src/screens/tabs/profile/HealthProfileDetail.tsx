import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../../navigations/StackNavigator';
import { colors, fonts, sizes } from '../../../theme/theme';
import { AppText, DropDownPicker } from '../../../componets';
import HealthProfileCard from '../../../componets/profile/HealthProfileCard';
import ToggleableTag from '../../../componets/common/ToggleableTag';
import {
  getHealthProfileData,
  HealthProfileData,
} from '../../../data/healthProfileData';

type Props = NativeStackScreenProps<StackParamList, 'HealthProfileDetail'>;

const HealthProfileDetail: React.FC<Props> = ({ route }) => {
  const { title } = route.params;

  // State for the entire health profile data
  const [profileData, setProfileData] = useState<HealthProfileData | null>(
    null,
  );

  // Initialize data based on the title
  React.useEffect(() => {
    const data = getHealthProfileData(title);
    if (data) {
      setProfileData(data);
    }
  }, [title]);

  const toggleRadioOption = (id: string) => {
    if (!profileData) return;

    setProfileData(prev => {
      if (!prev) return prev;
      return {
        ...prev,
        radioOptions: prev.radioOptions.map(option =>
          option.id === id ? { ...option, selected: !option.selected } : option,
        ),
      };
    });
  };

  const toggleTag = (sectionIndex: number, tag: string) => {
    if (!profileData) return;

    setProfileData(prev => {
      if (!prev) return prev;
      const updatedTagSections = [...prev.tagSections];
      const section = updatedTagSections[sectionIndex];

      if (section.selectedTags.includes(tag)) {
        section.selectedTags = section.selectedTags.filter(t => t !== tag);
      } else {
        section.selectedTags = [...section.selectedTags, tag];
      }

      return {
        ...prev,
        tagSections: updatedTagSections,
      };
    });
  };

  const handleDropdownChange = (dropdownIndex: number, value: string) => {
    if (!profileData) return;

    setProfileData(prev => {
      if (!prev) return prev;
      const updatedDropdowns = [...prev.dropdowns];
      updatedDropdowns[dropdownIndex] = {
        ...updatedDropdowns[dropdownIndex],
        selectedValue: value,
      };

      return {
        ...prev,
        dropdowns: updatedDropdowns,
      };
    });
  };

  if (!profileData) {
    return (
      <View style={styles.container}>
        <AppText>Loading...</AppText>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Header card */}
        <HealthProfileCard title={title} />

        {/* Radio options */}
        {profileData.radioOptions.length > 0 && (
          <View>
            {profileData.radioOptions.map(option => (
              <TouchableOpacity
                key={option.id}
                style={styles.radioRow}
                onPress={() => toggleRadioOption(option.id)}
              >
                <View
                  style={[
                    styles.radioButton,
                    option.selected && styles.radioButtonSelected,
                  ]}
                >
                  {option.selected && <View style={styles.radioButtonInner} />}
                </View>
                <AppText style={styles.radioLabel}>{option.label}</AppText>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Dropdowns */}
        {profileData.dropdowns.map((dropdown, index) => (
          <DropDownPicker
            key={index}
            label={dropdown.label}
            options={dropdown.options}
            selectedValue={dropdown.selectedValue}
            onSelect={value => handleDropdownChange(index, value)}
          />
        ))}

        {/* Tag sections */}
        {profileData.tagSections.map((section, sectionIndex) => (
          <View key={sectionIndex} style={styles.sectionContainer}>
            <AppText style={styles.sectionTitle}>{section.title}</AppText>
            <View style={styles.tagsContainer}>
              {section.tags.map((tag, tagIndex) => (
                <ToggleableTag
                  key={tagIndex}
                  item={tag}
                  selected={section.selectedTags.includes(tag)}
                  onPress={() => toggleTag(sectionIndex, tag)}
                  showCross={false}
                  tagContainerStyle={styles.tag}
                />
              ))}
              <TouchableOpacity>
                <AppText style={styles.addButtonText}>
                  {section.addButtonText}
                </AppText>
              </TouchableOpacity>
            </View>
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

export default HealthProfileDetail;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 18,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  // radioContainer: {
  //   marginTop: 0,
  // },
  radioRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 14,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.grey_50,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonSelected: {
    borderColor: colors.primary,
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: colors.primary,
  },
  radioLabel: {
    fontSize: 16,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_90,
  },
  // dropdownContainer: {
  //   marginTop: 10,
  // },
  sectionContainer: {
    marginTop: 10,
    marginHorizontal: 8,
    borderRadius: 12,
    paddingHorizontal: 10,
    paddingVertical: 5,
    backgroundColor: colors.background_color,

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,

    elevation: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: fonts.NotoSans_SemiBold,
    color: colors.grey_90,
    marginBottom: 10,
    marginTop: 5,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
  },
  tag: {
    marginRight: 0,
    marginBottom: 8,
    borderRadius: 3,
  },

  addButtonText: {
    padding: 7,
    fontSize: 12,
    borderColor: colors.grey_20,
    color: colors.grey_60,
    borderWidth: 1,
    borderRadius: 3,
  },
});
