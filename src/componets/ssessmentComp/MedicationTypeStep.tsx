import React from 'react';
import { View, FlatList, StyleSheet } from 'react-native';
import AssessmentSelector from './AssessmentSelector';
import AppText from '../common/AppText';
import AppButton from '../common/AppButton';
import assesmentStyles from './assesmentStyles';

interface Props {
  value: string[];
  onChange: (value: string[]) => void;
  onContinue: () => void;
  onPressBack: () => void;
}

const medicationTypeStep = [
  'Insulin Pump',
  'Oral antidiabetics',
  'GLP-1 Receptor Agonists',
  'SGLT2 Inhibitors',
  'Metformin',
  'Others',
];
const MedicationTypeStep: React.FC<Props> = ({
  value,
  onChange,
  onContinue,
  onPressBack,
}) => {
  const toggleSelection = (item: string) => {
    if (value.includes(item)) {
      onChange(value.filter(v => v !== item));
    } else {
      onChange([...value, item]);
    }
  };

  return (
    <View style={styles.container}>
      <AppText ml={4} style={[assesmentStyles.assessmentTitle]}>
        Which type of medications do you take ?{' '}
      </AppText>
      <View style={{ flex: 1 }}>
        <FlatList
          data={medicationTypeStep}
          keyExtractor={item => item}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={assesmentStyles.contentContainer}
          renderItem={({ item }) => (
            <AssessmentSelector
              option={item}
              selected={value.includes(item)}
              onPress={() => toggleSelection(item)}
            />
          )}
          ItemSeparatorComponent={() => <View style={{ height: 12 }} />}
        />
      </View>
      <AppButton
        title="Continue"
        onPress={onContinue}
        disabled={!value.length}
      />
      <AppButton
        title="Back"
        containerStyle={assesmentStyles.skipButton}
        titleStyle={assesmentStyles.skipButtonTitle}
        onPress={onPressBack}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default MedicationTypeStep;
