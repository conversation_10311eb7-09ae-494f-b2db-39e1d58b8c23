import {persistReducer, persistStore} from 'redux-persist';
import {appReducer} from './Reducer';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {configureStore} from '@reduxjs/toolkit';
import {
  useDispatch,
  useSelector as useReduxSelector,
  TypedUseSelectorHook,
} from 'react-redux';
const persistedReducer = persistReducer(
  {
    key: 'root',
    blacklist: ['config', 'appointments'],
    storage: AsyncStorage,
  },
  appReducer,
);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export type RootState = ReturnType<typeof store.getState>;

export type AppDispatch = typeof store.dispatch;
export const useAppDispatch: () => AppDispatch = useDispatch;
export const useAppSelector: TypedUseSelectorHook<RootState> = useReduxSelector;

export const persistor = persistStore(store);
