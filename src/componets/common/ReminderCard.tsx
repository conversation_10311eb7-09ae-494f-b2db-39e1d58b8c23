import { StyleSheet, Text, View, ViewStyle, TextStyle } from 'react-native';
import React from 'react';
import ToggleSwitch from 'toggle-switch-react-native';
import { colors, fonts } from '../../theme/theme';
import assesmentStyles from '../ssessmentComp/assesmentStyles';

type ReminderCardProps = {
  title: string;
  isOn: boolean;
  onToggle: (isOn: boolean) => void;
  containerStyle?: ViewStyle;
  titleStyle?: TextStyle;
};

const ReminderCard: React.FC<ReminderCardProps> = ({
  title,
  isOn,
  onToggle,
  containerStyle,
  titleStyle,
}) => {
  return (
    <View style={[styles.container, containerStyle]}>
      <Text
        numberOfLines={1}
        style={[assesmentStyles.assessmentTitle, { width: '84%' }, titleStyle]}
      >
        {title}
      </Text>
      <ToggleSwitch
        isOn={isOn}
        onColor={colors.primary}
        offColor={colors.grey_50}
        labelStyle={styles.label}
        size="small"
        onToggle={onToggle}
      />
    </View>
  );
};

export default ReminderCard;

const styles = StyleSheet.create({
  container: {
    ...assesmentStyles.cardContainer,
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: 20,
    width: '100%',
  },
  label: {
    color: 'black',
    fontWeight: '900',
  },
});
