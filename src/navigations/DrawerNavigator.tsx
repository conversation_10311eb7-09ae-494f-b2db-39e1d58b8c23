import React from 'react';
import { createDrawerNavigator } from '@react-navigation/drawer';
import StackNavigator from './StackNavigator';
import { colors, sizes } from '../theme/theme';
import CustomDrawerContent from './CustomDrawerContent';

const Drawer = createDrawerNavigator();

const DrawerNavigator = () => {
  return (
    <Drawer.Navigator
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          backgroundColor: 'transparent', // gradient will be applied in content
          width: sizes.width * 0.85,
          borderTopRightRadius: 26,
          borderBottomRightRadius: 26,
        },
        overlayColor: 'rgba(0,0,0,0.3)',
        swipeEnabled: false,
      }}
      drawerContent={props => <CustomDrawerContent {...props} />}
    >
      <Drawer.Screen name="HomeStack" component={StackNavigator} />
      {/* You can add more screens like Help, Settings, etc. here if needed */}
    </Drawer.Navigator>
  );
};

export default DrawerNavigator;
