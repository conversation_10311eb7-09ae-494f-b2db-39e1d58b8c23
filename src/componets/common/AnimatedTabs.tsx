import {
  Dimensions,
  StyleSheet,
  View,
  TouchableOpacity,
  Animated,
  ViewStyle,
} from 'react-native';
import React, { useEffect, useRef } from 'react';
import { colors, fonts } from '../../theme/theme';

interface Props {
  activeTab: string;
  setActiveTab: (val: string) => void;
  TABS: string[];
  containerStyle?: ViewStyle;
}

const AnimatedTabs: React.FC<Props> = ({
  activeTab,
  setActiveTab,
  TABS,
  containerStyle,
}) => {
  const screenWidth = Dimensions.get('screen').width;
  const tabWidth = screenWidth / 2 - 24;

  const translateX = useRef(new Animated.Value(0)).current;

  // Initialize animated values for each tab's font size and color
  const fontSizes = useRef(
    TABS.map(tab => new Animated.Value(tab === activeTab ? 1 : 0)),
  ).current;

  useEffect(() => {
    const activeIndex = TABS.findIndex(tab => tab === activeTab);

    // Animate indicator and font sizes
    Animated.parallel([
      Animated.timing(translateX, {
        toValue: activeIndex * tabWidth,
        duration: 300,
        useNativeDriver: true,
      }),
      ...fontSizes.map((animVal, i) =>
        Animated.timing(animVal, {
          toValue: i === activeIndex ? 1 : 0,
          duration: 300,
          useNativeDriver: false,
        }),
      ),
    ]).start();
  }, [activeTab, tabWidth]);

  return (
    <View style={[styles.tabsContainer, containerStyle]}>
      {/* Animated indicator */}
      <Animated.View
        style={[
          styles.indicator,
          {
            width: tabWidth,
            transform: [{ translateX }],
            left: activeTab == TABS[0] ? 4 : -4,
          },
        ]}
      />

      {TABS.map((tab, index) => {
        const fontSize = fontSizes[index].interpolate({
          inputRange: [0, 1],
          outputRange: [12, 14],
        });

        const color = fontSizes[index].interpolate({
          inputRange: [0, 1],
          outputRange: ['#131212', colors.primary],
        });

        return (
          <TouchableOpacity
            key={tab}
            style={[styles.tabContainer, { width: tabWidth }]}
            onPress={() => setActiveTab(tab)}
          >
            <Animated.Text
              style={{
                fontSize,
                color,
                fontFamily:
                  activeTab === tab
                    ? fonts.NotoSans_Bold
                    : fonts.NotoSans_Medium,
              }}
            >
              {tab}
            </Animated.Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

export default AnimatedTabs;

const styles = StyleSheet.create({
  tabsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    position: 'relative',
    borderRadius: 10,
    backgroundColor: '#ABABAB33',
    height: 48,
    marginBottom: 12,
    overflow: 'hidden',
  },
  tabContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  indicator: {
    position: 'absolute',
    height: 40,
    backgroundColor: colors.white,
    bottom: 4,
    borderRadius: 10,
    left: 0,
  },
});
