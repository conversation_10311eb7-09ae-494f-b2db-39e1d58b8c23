import React from 'react';
import { TouchableOpacity, StyleSheet, View } from 'react-native';
import { colors } from '../../theme/theme';
import AppText from '../common/AppText';
import { CheckSqure } from '../../assets/svgs';
import assesmentStyles from './assesmentStyles';

interface Props {
  option: string;
  selected: boolean;
  onPress: () => void;
}

const AssessmentSelector: React.FC<Props> = ({ option, selected, onPress }) => {
  return (
    <TouchableOpacity
      activeOpacity={0.5}
      style={[
        assesmentStyles.outerBorder,
        { backgroundColor: selected ? '#9ADBF2' : colors.white },
      ]}
      onPress={onPress}
    >
      <View
        style={[
          assesmentStyles.innerBorder,
          { borderColor: selected ? colors.primary : colors.white },
        ]}
      >
        <AppText style={assesmentStyles.optionText}>{option}</AppText>
        {selected && <CheckSqure />}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({});

export default AssessmentSelector;
