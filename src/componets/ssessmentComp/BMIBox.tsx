import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import AppText from '../common/AppText';
import assesmentStyles from './assesmentStyles';
import { colors as appColor, appStyles, fonts } from '../../theme/theme';
import AppButton from '../common/AppButton';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const colors: Record<string, string> = {
  underweight: '#36BFFA',
  normal: '#12B76A',
  overweight: '#FDB022',
  obese: '#F97066',
  extreme: '#D92D20',
  background: '#030303ff',
  text: '#131212',
};

function getBMIColor(category: string): string {
  return colors[category] || '#000000'; // default to black if key not found
}

interface BMIBoxProps {
  weight: number;
  weightUnit: 'kg' | 'lb';
  height: number; // heightCm or totalInches
  heightUnit: 'cm' | 'in'; // inches (total) or cm
  bodyFat?: number;
  onContinue: () => void;
  onChange: (bmi: number) => void;
  onPressBack: () => void;
}

const BMIBox: React.FC<BMIBoxProps> = ({
  weight,
  weightUnit,
  height,
  heightUnit,
  bodyFat,
  onContinue,
  onChange,
  onPressBack,
}) => {
  // console.log('weight-------', weight);
  // console.log('weightUnit----', weightUnit);
  // console.log('height----', height);
  // console.log('heightUnit---', heightUnit);
  // console.log('bodyFat---', bodyFat);

  // Convert to kg
  const weightKg = weightUnit === 'lb' ? weight * 0.453592 : weight;

  // Convert to meters
  const heightM = heightUnit === 'cm' ? height / 100 : (height * 2.54) / 100;

  const bmi = +(weightKg / heightM ** 2).toFixed(1);
  const category = getBMICategory(bmi);

  const getColorForBMI = () => {
    if (bmi < 18.5) return colors.underweight;
    if (bmi < 24.9) return colors.normal;
    if (bmi < 29.9) return colors.overweight;
    if (bmi < 34.9) return colors.obese;
    return colors.extreme;
  };
  const insets = useSafeAreaInsets();

  return (
    <View style={styles.container}>
      <View style={{ flex: 1 }}>
        <AppText style={assesmentStyles.assessmentTitle}>
          Your BMI is within the{' '}
          <AppText color={appColor.primary}>{category.toLowerCase()}</AppText>{' '}
          range
        </AppText>

        <View style={styles.card}>
          <View style={appStyles.flexRow}>
            <AppText style={[appStyles.h3, { flex: 1 }]}>
              Current Weight{' '}
              <Text style={styles.unit}>
                {' '}
                {weightUnit === 'lb' ? 'Lb' : 'Kg'}
              </Text>
            </AppText>
            <AppText style={[appStyles.h4, { textTransform: 'capitalize' }]}>
              {category.toLowerCase()}
            </AppText>
          </View>
          <View style={styles.row}>
            <View style={styles.column}>
              <Text style={styles.label}>Weight</Text>
              <Text style={styles.value}>{weight}</Text>
            </View>

            <View style={styles.column}>
              <Text style={styles.label}>BMI</Text>
              <Text style={[styles.value, { color: getColorForBMI() }]}>
                {bmi}
              </Text>
            </View>

            <View style={styles.column}>
              <Text style={styles.label}>Body fat</Text>
              <Text style={styles.value}>{bodyFat ?? '-'}%</Text>
            </View>
          </View>

          {/* BMI Range Bar */}
          <View style={styles.rangeBar}>
            {['underweight', 'normal', 'overweight', 'obese', 'extreme'].map(
              range => (
                <View
                  key={range}
                  style={[
                    styles.rangeSegment,
                    { backgroundColor: colors[range as keyof typeof colors] },
                  ]}
                />
              ),
            )}
            {/* Triangle Pointer */}
          </View>
          {/* Triangle Pointer */}
          <View
            style={[
              styles.triangle,
              { left: `${getBMIPointerPosition(bmi)}%` },
            ]}
          />
        </View>
      </View>
      <AppButton title="Continue" onPress={onContinue} />
      <AppButton
        title="Back"
        containerStyle={assesmentStyles.skipButton}
        titleStyle={assesmentStyles.skipButtonTitle}
        onPress={onPressBack}
      />
    </View>
  );
};

const getBMIPointerPosition = (bmi: number) => {
  // Map BMI to approximate position in %
  if (bmi <= 16) return 0;
  if (bmi <= 18.5) return 10;
  if (bmi <= 24.9) return 30;
  if (bmi <= 29.9) return 55;
  if (bmi <= 34.9) return 75;
  return 90;
};

const getBMICategory = (bmi: number) => {
  if (bmi < 18.5) return 'Underweight';
  if (bmi < 24.9) return 'Normal';
  if (bmi < 29.9) return 'Overweight';
  if (bmi < 34.9) return 'Obese';
  return 'Extremely Obese';
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: appColor.background_color,
    flex: 1,
  },

  card: {
    backgroundColor: appColor.white,
    borderRadius: 12,
    padding: 10,
    paddingBottom: 36,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,

    elevation: 1,
    marginTop: 20,
  },

  unit: {
    color: appColor.grey_50,
    fontSize: 12,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 6,
  },
  column: {
    alignItems: 'center',
  },
  label: {
    fontSize: 12,
    color: appColor.grey_30,
    fontFamily: fonts.NotoSans_Regular,
  },
  value: {
    fontSize: 18,
    color: appColor.grey_80,
    fontFamily: fonts.NotoSans_SemiBold,
    lineHeight: 30,
  },
  rangeBar: {
    flexDirection: 'row',
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
    marginTop: 24,
    position: 'relative',
  },
  rangeSegment: {
    flex: 1,
    borderRadius: 4,
  },
  triangle: {
    position: 'absolute',
    width: 0,
    height: 0,
    borderLeftWidth: 6,
    borderRightWidth: 6,
    borderTopWidth: 6,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: '#000',
    bottom: 54,
    marginLeft: -6,
    zIndex: 100,
  },
});

export default BMIBox;
