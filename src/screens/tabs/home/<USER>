import { ScrollView, StatusBar, StyleSheet, Text, View } from 'react-native';
import React, { useState } from 'react';
import { colors, fonts, sizes } from '../../../theme/theme';
import TopAnimatedTab from '../../../componets/common/TopAnimatedTab';
import { EstimatedA1C, GlucoseLineChart } from '../../../componets';

const BloodGlucose = () => {
  const [activeTab, setActiveTab] = useState('Logs');
  const TABS = ['Logs', 'Graph'];

  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          backgroundColor: colors.background_color,
          paddingBottom: 34,
        }}
      >
        <StatusBar
          backgroundColor={colors.background_color}
          barStyle={'dark-content'}
        />
        <View style={{ paddingHorizontal: sizes.paddingHorizontal }}>
          <TopAnimatedTab
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            TABS={TABS}
            containerStyle={{ marginTop: 12 }}
          />
        </View>

        <GlucoseLineChart />
        <EstimatedA1C />
      </ScrollView>
    </View>
  );
};

export default BloodGlucose;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.background_color,
    flex: 1,
  },
});
