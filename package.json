{"name": "vitai_chek", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@gorhom/bottom-sheet": "^5", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native/new-app-screen": "0.80.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/drawer": "^7.5.3", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "@reduxjs/toolkit": "^2.8.2", "@shopify/flash-list": "^1.8.3", "formik": "^2.4.6", "moment": "^2.30.1", "react": "19.1.0", "react-native": "0.80.1", "react-native-app-intro-slider": "^4.0.4", "react-native-calendars": "^1.1313.0", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "^2.27.1", "react-native-gifted-charts": "^1.4.63", "react-native-image-crop-picker": "^0.50.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.8.3", "react-native-progress": "^5.0.1", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.11.1", "react-native-svg": "^15.12.0", "react-native-svg-transformer": "^1.5.1", "react-native-toast-message": "^2.3.3", "react-native-vector-icons": "^10.2.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "toggle-switch-react-native": "^3.3.0", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.0.0", "@react-native-community/cli-platform-android": "19.0.0", "@react-native-community/cli-platform-ios": "19.0.0", "@react-native/babel-preset": "0.80.1", "@react-native/eslint-config": "0.80.1", "@react-native/metro-config": "0.80.1", "@react-native/typescript-config": "0.80.1", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-test-renderer": "^19.1.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}