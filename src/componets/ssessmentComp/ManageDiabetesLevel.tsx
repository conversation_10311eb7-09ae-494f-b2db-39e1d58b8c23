import React, { useRef, useState } from 'react';
import {
  View,
  StyleSheet,
  Animated,
  Pressable,
  PanResponder,
} from 'react-native';
import { colors, fonts, sizes } from '../../theme/theme';
import { DoubleArrow } from '../../assets/svgs';
import AppText from '../common/AppText';
import assesmentStyles from './assesmentStyles';
import AppButton from '../common/AppButton';

interface Props {
  value: string;
  onChange: (value: string) => void;
  onContinue: () => void;
  onPressBack: () => void;
}

const SLIDER_WIDTH = sizes.width - sizes.paddingHorizontal * 2;
const SLIDER_HEIGHT = 68;
const NUM_LEVELS = 5;

const ManageDiabetesLevel: React.FC<Props> = ({
  value,
  onChange,
  onContinue,
  onPressBack,
}) => {
  const indicatorWidth = SLIDER_WIDTH / NUM_LEVELS;

  const [currentLevel, setCurrentLevel] = useState(2);
  const [isVisible, setIsVisible] = useState(true); // for hiding indicator when dragged out
  const [liveLevel, setLiveLevel] = useState(currentLevel); // real-time level display

  const filledColor = '#0A7CBA';
  const unfilledColor = '#D4D4D4';

  const indicatorPosition = useRef(
    new Animated.Value((currentLevel - 1) * indicatorWidth),
  ).current;
  const lastOffset = useRef((currentLevel - 1) * indicatorWidth);

  const moveToLevel = (index: number) => {
    const toValue = index * indicatorWidth;
    lastOffset.current = toValue;

    Animated.spring(indicatorPosition, {
      toValue,
      useNativeDriver: true,
    }).start();

    setCurrentLevel(index + 1);
    setLiveLevel(index + 1);
    setIsVisible(true);
  };

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,

      onPanResponderGrant: () => {
        indicatorPosition.setOffset(lastOffset.current);
        indicatorPosition.setValue(0);
      },

      onPanResponderMove: (_, gestureState) => {
        let rawX = lastOffset.current + gestureState.dx;

        // Clamp value between 0 and max allowed
        const clampedX = Math.max(
          0,
          Math.min(rawX, SLIDER_WIDTH - indicatorWidth),
        );

        indicatorPosition.setValue(clampedX - lastOffset.current);

        const tempLevel = Math.round(clampedX / indicatorWidth) + 1;
        const clampedLevel = Math.max(1, Math.min(NUM_LEVELS, tempLevel));
        setLiveLevel(clampedLevel);
      },

      onPanResponderRelease: (_, gestureState) => {
        indicatorPosition.flattenOffset();
        let finalX = lastOffset.current + gestureState.dx;
        finalX = Math.max(0, Math.min(finalX, SLIDER_WIDTH - indicatorWidth)); // Clamp again
        const newLevel = Math.round(finalX / indicatorWidth);
        moveToLevel(newLevel);
      },
    }),
  ).current;

  return (
    <View style={styles.container}>
      <View style={{ flex: 1 }}>
        <AppText style={assesmentStyles.assessmentTitle}>
          How motivated are you to manage your diabetes?{' '}
        </AppText>
        <AppText style={assesmentStyles.assessmentTitle}>
          on a Scale of 1 to {currentLevel}
        </AppText>

        <View style={styles.sliderWrapper}>
          <View style={styles.track}>
            {Array.from({ length: NUM_LEVELS }).map((_, index) => (
              <Pressable
                key={index}
                onPress={() => moveToLevel(index)}
                style={{ width: indicatorWidth, alignItems: 'center' }}
              >
                <View style={styles.tick} />
              </Pressable>
            ))}

            {isVisible && (
              <Animated.View
                style={[
                  styles.indicator,
                  {
                    width: indicatorWidth,
                    transform: [{ translateX: indicatorPosition }],
                  },
                ]}
                {...panResponder.panHandlers}
              >
                <DoubleArrow />
              </Animated.View>
            )}
          </View>
        </View>
        <View style={[assesmentStyles.cardContainer, { marginTop: 12 }]}>
          <AppText style={assesmentStyles.assessmentTitle}>
            Level {liveLevel}
          </AppText>
        </View>
        <AppText style={styles.dayText}>2 time /Day </AppText>
      </View>
      <AppButton title="Continue" onPress={onContinue} disabled={!value} />
      <AppButton
        title="Back"
        containerStyle={assesmentStyles.skipButton}
        titleStyle={assesmentStyles.skipButtonTitle}
        onPress={onPressBack}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
  },
  sliderWrapper: {
    width: SLIDER_WIDTH,
    height: SLIDER_HEIGHT,
    backgroundColor: '#0873B41A',
    borderRadius: 16,
    justifyContent: 'center',
    overflow: 'hidden',
    alignSelf: 'center',
    marginTop: 12,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: colors.white,
  },
  track: {
    flexDirection: 'row',
    alignItems: 'center',
    height: '100%',
  },
  tick: {
    width: 3.5,
    height: 38,
    backgroundColor: '#94A3B8',
    borderRadius: 4,
  },
  indicator: {
    position: 'absolute',
    top: 0,
    height: SLIDER_HEIGHT,
    backgroundColor: '#0A7CBA',
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },

  dayText: {
    fontSize: 12,
    color: colors.eastBay,
    fontFamily: fonts.Catamaran_SemiBold,
    paddingTop: 5,
    textAlign: 'right',
  },
});

export default ManageDiabetesLevel;
