<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>vitai_chek</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string></string>
	<key>RCTNewArchEnabled</key>
	<true/>
	<key>UIAppFonts</key>
	<array>
		<string>Catamaran-Bold.ttf</string>
		<string>Catamaran-Light.ttf</string>
		<string>Catamaran-Medium.ttf</string>
		<string>Catamaran-Regular.ttf</string>
		<string>Catamaran-SemiBold.ttf</string>
		<string>NotoSans-Bold.ttf</string>
		<string>NotoSans-Light.ttf</string>
		<string>NotoSans-Medium.ttf</string>
		<string>NotoSans-MediumItalic.ttf</string>
		<string>NotoSans-Regular.ttf</string>
		<string>NotoSans-SemiBold.ttf</string>
		<string>NotoSans_SemiCondensed-LightItalic.ttf</string>
		<string>Roboto-Bold.ttf</string>
		<string>Roboto-Light.ttf</string>
		<string>Roboto-Medium.ttf</string>
		<string>Roboto-Regular.ttf</string>
		<string>Roboto-SemiBold.ttf</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
