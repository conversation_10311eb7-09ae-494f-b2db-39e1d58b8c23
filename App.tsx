import { LogBox, StatusBar, View } from 'react-native';
import React from 'react';
import RootStackNavigator from './src/navigations/RootStackNavigator';
import { colors } from './src/theme/theme';
import StoreContext from './src/redux/config/StoreContext';

const App = () => {
  LogBox.ignoreLogs([
    'VirtualizedLists should never be nested inside plain ScrollViews',
  ]);
  return (
    <StoreContext>
      <View style={{ flex: 1, backgroundColor: colors.background_color }}>
        <StatusBar
          backgroundColor={colors.background_color}
          barStyle={'dark-content'}
        />
        <RootStackNavigator />
      </View>
    </StoreContext>
  );
};

export default App;
