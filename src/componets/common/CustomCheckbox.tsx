import React from 'react';
import {
  Text,
  Pressable,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { CheckSqure, CheckStroke } from '../../assets/svgs';
import { fonts } from '../../theme/theme';

type Props = {
  checked: boolean;
  onToggle: () => void;
  label: string | boolean;
  containerStyle?: ViewStyle;
  fillCheck?: {
    width: number;
    height: number;
  };
  strokeCheck?: {
    width: number;
    height: number;
  };
  labelStyles?: TextStyle;
};

const CustomCheckbox: React.FC<Props> = ({
  checked,
  onToggle,
  label,
  containerStyle,
  fillCheck,
  strokeCheck,
  labelStyles,
}) => {
  return (
    <Pressable onPress={onToggle} style={[styles.container, containerStyle]}>
      {checked ? (
        <CheckSqure
          width={fillCheck?.width ? fillCheck?.width : 12}
          height={fillCheck?.height ? fillCheck?.height : 12}
        />
      ) : (
        <CheckStroke
          width={strokeCheck?.width ? strokeCheck?.width : 12}
          height={strokeCheck?.height ? strokeCheck?.height : 12}
        />
      )}
      <Text style={[styles.label, labelStyles]}>{label}</Text>
    </Pressable>
  );
};

export default CustomCheckbox;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  label: {
    fontSize: 12,
    color: '#858583',
    fontFamily: fonts.NotoSans_Regular,
    paddingTop: 2,
  },
});
