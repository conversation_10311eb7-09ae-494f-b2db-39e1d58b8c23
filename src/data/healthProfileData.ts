export interface RadioOption {
  id: string;
  label: string;
  selected: boolean;
}

export interface DropdownOption {
  label: string;
  value: string;
}

export interface DropdownConfig {
  label: string;
  selectedValue: string;
  options: DropdownOption[];
}

export interface TagSection {
  title: string;
  tags: string[];
  selectedTags: string[];
  addButtonText: string;
}

export interface HealthProfileData {
  title: string;
  radioOptions: RadioOption[];
  dropdowns: DropdownConfig[];
  tagSections: TagSection[];
}

const healthProfileConfigs: { [key: string]: HealthProfileData } = {
  'My condition': {
    title: 'My condition',
    radioOptions: [
      { id: 'diabetes', label: 'Diabetes', selected: false },
      { id: 'hypertension', label: 'Hypertension', selected: true },
      { id: 'weight', label: 'Weight management', selected: false },
    ],
    dropdowns: [
      {
        label: 'Diabetes type',
        selectedValue: 'gestational',
        options: [
          { label: 'Type 1', value: 'type1' },
          { label: 'Type 2', value: 'type2' },
          { label: 'Gestational', value: 'gestational' },
          { label: 'Pre-diabetes', value: 'prediabetes' },
        ],
      },
      {
        label: 'Hypertension',
        selectedValue: 'gestational',
        options: [
          { label: 'Primary', value: 'primary' },
          { label: 'Secondary', value: 'secondary' },
          { label: 'Gestational', value: 'gestational' },
          { label: 'White coat', value: 'whitecoat' },
        ],
      },
    ],
    tagSections: [
      {
        title: 'Other Conditions',
        tags: [
          'None',
          'Hgh lipids',
          'Chronic kidney',
          'Chronic pain',
          'physical disability',
          'cancer',
          'Cardiovascular',
          'Heart problem',
        ],
        selectedTags: ['Hgh lipids'],
        addButtonText: '+ Personnalise',
      },
    ],
  },
  'Meds & supplements': {
    title: 'Meds & supplements',
    radioOptions: [
      { id: 'pen', label: 'Pen/Syringe', selected: false },
      { id: 'pump', label: 'Pump', selected: true },
      { id: 'none', label: 'None', selected: false },
    ],
    dropdowns: [
      {
        label: 'Bolus(fast-acting)',
        selectedValue: 'humalog',
        options: [
          { label: 'Humalog', value: 'humalog' },
          { label: 'NovoLog', value: 'novolog' },
          { label: 'Apidra', value: 'apidra' },
          { label: 'Fiasp', value: 'fiasp' },
        ],
      },
      {
        label: 'Basal(Long-acting)',
        selectedValue: 'novorapid',
        options: [
          { label: 'NoVo Rapid', value: 'novorapid' },
          { label: 'Lantus', value: 'lantus' },
          { label: 'Levemir', value: 'levemir' },
          { label: 'Tresiba', value: 'tresiba' },
        ],
      },
    ],
    tagSections: [
      {
        title: 'Oral medications',
        tags: [
          'None',
          'High lipids',
          'Chronic kidney d',
          'Chronic pain',
          'physical disability',
          'Cancer',
          'Cardiovascular',
        ],
        selectedTags: ['High lipids'],
        addButtonText: '+ Add New Med',
      },
      {
        title: 'Supplements',
        tags: [
          'None',
          'High lipids',
          'Chronic kidney d',
          'Chronic pain',
          'physical disability',
          'Cancer',
          'Cardiovascular',
        ],
        selectedTags: ['High lipids'],
        addButtonText: '+ Add New Med',
      },
    ],
  },
  Lifestyle: {
    title: 'Lifestyle',
    radioOptions: [],
    dropdowns: [],
    tagSections: [
      {
        title: 'Physical Activity',
        tags: [
          'Walking',
          'Running',
          'Swimming',
          'Cycling',
          'Yoga',
          'Weight training',
          'Dancing',
          'Sports',
        ],
        selectedTags: ['Walking', 'Swimming'],
        addButtonText: '+ Add Custom',
      },
      {
        title: 'Diet & Nutrition',
        tags: [
          'Mediterranean',
          'Low carb',
          'Vegetarian',
          'Vegan',
          'Keto',
          'Paleo',
          'Intermittent fasting',
          'No specific diet',
        ],
        selectedTags: ['Mediterranean'],
        addButtonText: '+ Add',
      },
      {
        title: 'Sleep Pattern',
        tags: [
          'Less than 6 hours',
          '6-7 hours',
          '7-8 hours',
          '8-9 hours',
          'More than 9 hours',
        ],
        selectedTags: ['7-8 hours'],
        addButtonText: '+ Add',
      },
      {
        title: 'Stress Management',
        tags: [
          'Meditation',
          'Deep breathing',
          'Exercise',
          'Music',
          'Reading',
          'Socializing',
          'Hobbies',
          'Professional help',
        ],
        selectedTags: [],
        addButtonText: '+ Add Method',
      },
    ],
  },
};

export const getHealthProfileData = (title: string): HealthProfileData | null => {
  return healthProfileConfigs[title] || null;
};
